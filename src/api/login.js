import request from '@/utils/request'

// 登录方法
export function login(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'BA003', ...data},
    loading: true
  })
}
export function loginOnekey(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'BA008', ...data},
    loading: true
  })
}
// 获取用户详细信息
export function getInfo() {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'SF003'},
  })
}

// 退出方法
export function logout() {
  return request({
    url: '/logout',
    method: 'post'
  })
}

// 注册
export function register(data) {
  return request({
    url: '/mobile/register',
    headers: {
      isToken: false
    },
    method: 'post',
    data: data,
    loading: true
  })
}
