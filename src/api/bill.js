import request from '@/utils/request'
//列表
export function billList(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'BL301', ...data},
    loading: true
  })
}

export function billDetail(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'BL302', ...data},
    loading: true
  })
}
export function AutomaticRepayment(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'BL303', ...data},
    loading: true
  })
}
export function getRecoveryMode(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS016', ...data}
  })
}
// 通过订单号查询账单信息
export function billDetailByOrderId(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'BL307', ...data},
    loading: true
  })
}
