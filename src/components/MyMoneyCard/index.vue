<template>
  <div v-if="quotaInfo?.productId" class="money-member">
    <div class="title">我的钱包
       <img  v-if="isShowQuota" @click="isShowQuota = false" src="@/assets/images/my/My-money-card-show.png" alt="" />
       <img v-else @click="isShowQuota = true" src="@/assets/images/my/My-money-card-hide.png" alt="" />
    </div>
    <div class="content">
      <div class="item_top">
        <div class="item">
          <div class="l" @click="goCreditQuotaRecord">
            <div class="money">
              ￥
              <span v-if="quotaInfo.creditStatus === 'REFUSE'">0.00</span>
              <span v-else-if="quotaInfo.creditStatus === 'NON'||quotaInfo.creditStatus === 'APPLY'">168,000</span>
              <span v-else-if="isShowQuota">{{ quotaInfo.creditAmount }}</span>
              <span v-else>****</span>
            </div>
            <div v-if="quotaInfo.creditStatus === 'NON'||quotaInfo.creditStatus === 'APPLY'||quotaInfo.creditStatus === 'REFUSE'" class="text">最高预估额度</div>
            <div v-else class="text">可用额度
              <!-- 用信逾期 -->
              <span v-if="quotaInfo.isOverdue=='Y'">已冻结</span>
              <span v-else-if="quotaInfo.creditStatus === 'INVALID'">已过期</span>
              </div>
          </div>
          <van-icon
            style=" margin-right: 10px;color: #999DAD;"
            name="arrow"
          />
        </div>
        <van-divider style="height: 53px" vertical />
        <div class="item r" @click="router.push('/my/consume-bill')">
          <div class="l pd">
            <div class="money">￥<span v-if="quotaInfo.creditStatus === 'NON'||quotaInfo.creditStatus === 'UNREALNAME'">--</span>
              <span v-else-if="isShowQuota">{{ quotaInfo.repayAmount }}</span>
              <span v-else>****</span></div>
            <div class="text">待还账单
              <span v-if="quotaInfo.isOverdue=='Y'">已逾期{{quotaInfo.overdueDays}}天</span>
            </div>
          </div>
        </div>
      </div>
      <!-- <div class="item_foo">¥1000临时额度有效期至2025年07月15日</div> -->
    </div>

    <van-notice-bar v-if="quotaInfo.creditStatus === 'NOPASS'||quotaInfo.creditStatus === 'REFUSE'" style="padding: 0" scrollable color="#999DAD" background="#fff">
      <!-- 左侧图标 -->
      <!-- <template #left-icon>
        <van-icon name="volume-o" color="#999DAD" />
      </template> -->
      <!-- 通知内容 -->
      🛒 抱歉～信用评分不足被拒，建议通过日常消费提升活跃度
    </van-notice-bar>
    <div class="footer_box" v-else>
      <div class="l">
        <!-- 逾期也不显示 -->
        <img v-if="quotaInfo.isOverdue=='N'&&quotaInfo.creditStatus !== 'INVALID'" src="@/assets/images/my/qxh-card-icon.png" alt="" />
        <div class="card_name" v-if="quotaInfo.isOverdue=='N'&&quotaInfo.creditStatus !== 'INVALID'">{{ quotaInfo.productName }}</div>
        <!-- 冻结（逾期） -->
        <div class="card_info" v-if="quotaInfo.isOverdue=='Y'">🌱 信用需要呵护，记得还款呀</div>
        <div class="card_info" v-else-if="quotaInfo.creditStatus === 'NON'||quotaInfo.creditStatus === 'UNREALNAME '">最高可借￥168,000</div>
        <div class="card_info" v-else-if="quotaInfo.creditStatus === 'APPLY'||quotaInfo.creditStatus === 'VERIF'">正在审核中，请稍后查看</div>
        <div class="card_info" v-else-if="quotaInfo.creditStatus === 'PASS'">花要尽兴，更要放心</div>
      </div>
      <div class="r">
        <van-button v-if="quotaInfo.creditStatus === 'NON'||quotaInfo.creditStatus === 'UNREALNAME'||quotaInfo.creditStatus === 'INVALID'" class="apply-btn" @click="apply()">
          {{quotaInfo.creditStatus === 'INVALID'?'重新激活':'去申请'}}
          <van-icon name="arrow" />
        </van-button>
      </div>
    </div>
  </div>
  <AuthorizePopup ref="authorizePopup" :title="quotaInfo?.productName" />
</template>

<script setup>
import { until, useAsyncState, whenever } from '@vueuse/core'
import { creditQuota, saveContacts,getBillList } from '@/api/customer'
import { listOrder } from '@/api/cashloan'
import global from '@/constant/Global.js'
import AuthorizePopup from '@/views/cashier/components/AuthorizePopup.vue'
import { nextTick, onActivated, onMounted, ref, useTemplateRef } from 'vue'
import { useRouteQuery } from '@vueuse/router'
import { showToast } from 'vant'

const { proxy } = getCurrentInstance()
const store = useStore()
const router = useRouter()
const route = useRoute()
const user = computed(() => store.getters.userInfo)
const props = defineProps({
  moneyData: {
    type: Object,
    default: () => {},
  },
})
// 是否显示额度
const isShowQuota = ref(true)

const authorizePopup = useTemplateRef('authorizePopup')
const isAuthBack = useRouteQuery('isAuthBack', false, { transform: Boolean })

const {
  state: quotaInfo,
  isLoading: quotaIsLoading,
  execute: refreshQuota,
  isReady: quotaIsReady,
} = useAsyncState(
  async () => {
    if (!user.value?.id) return null
    if (user.value.creditIntentionFlag !== 'Y') return null
    const { data } = await creditQuota(
      {
        requestType: 'query',
        sceneCode: global.CREDIT_SCENE_CONSUME,
      },
      false
    )

    const { data:billData } = await getBillList()




    return {...data,...billData}
  },
  null,
  {
    resetOnExecute: false,
    onSuccess(data) {
      console.log(data)
    },
  }
)

whenever(user, () => {
  refreshQuota()
})

const { execute: apply, isLoading: applying } = useAsyncState(
  async () => {
    // if (quotaInfo.value.creditStatus !== 'NON') {
    //   return
    // }
    if (user.value.flow !== global.USER_FLOW_FINISH) {
      const backRoute = router.resolve({
        path: router.path,
        query: {
          ...router.query,
          isAuthBack: true,
        },
        href: router.href,
      })
      // 待实名
      router.push({
        path: '/credit-facilities',
        query: {
          bankChannelId: quotaInfo.value.bankChannelId,
          cmId: quotaInfo.value.cmId,
          productId: quotaInfo.value.productId,
          back: backRoute.href,
        },
      })
      return
    }

    if (user.value.contactsFlag !== 'Y') {
      const { isCanceled, data } = await authorizePopup.value.reveal()
      if (!isCanceled && data?.phoneInfo) {
        saveContacts({ contacts: data.phoneInfo }).then(() => {
          store.dispatch('GetInfo')
        })
      }
    }

    await creditQuota(
      {
        requestType: 'credit',
        sceneCode: global.CREDIT_SCENE_CONSUME,
      },
      false
    )

    // showToast('额度申请提交成功')
    if (isAuthBack.value) {
      isAuthBack.value = false
      // await router.replace({
      //   query: {
      //     ...route.query,
      //     isAuthBack: false,
      //   },
      // })
      // await nextTick()
      await new Promise((resolve) => setTimeout(resolve, 300))
    }
    router.push({
      name: 'ConsumeApplyResult',
    })
    refreshQuota()
  },
  null,
  {
    immediate: false,
    onError: console.log,
  }
)
const goCreditQuotaRecord = () => {
  if (quotaInfo.value.creditStatus === 'PASS') {
    router.push({
      name: 'CreditQuotaRecord',
      params: {
        sceneCode: 'CONSUME',
      },
    })
  }
}

defineExpose({
  refresh: refreshQuota,
})

onMounted(async () => {

})
onActivated(async () => {
  if (isAuthBack.value) {
    await until(quotaIsReady).toBe(true)
    apply()
  }
})
</script>

<style lang="scss" scoped>
.money-member {
  border-radius: 10px;
  margin-top: 16px;
  background-color: #fff;
  background-image: url('@/assets/images/my/My-money-card-bg.png');
  background-repeat: no-repeat;
  background-position: center top;
  background-size: contain;
  padding: 13px 14px;
  margin: 13px 10px 0;
  box-sizing: border-box;
  font-size: 14px;
  .title {
    color: #333;
    font-size: 16px;
    font-weight: 600;
    line-height: normal;
    img {
      width: 12.5px;
      margin-left: 6px;
    }
  }
  .content {
    width: 100%;
    border-bottom: 1px solid #e3e3e3;
    border-top: 1px solid #e3e3e3;
    margin-top: 11.5px;
    display: flex;
    padding: 12px 0 21px 0;
    flex-direction: column;
    .item_top {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .item {
        height: 53px;
        display: flex;
        padding-left: 8px;
        align-items: center;
        justify-content: space-between;
        flex: 1;

        .l {
          flex-direction: column;
          // align-items: center;
          justify-content: space-between;
          &.pd {
            padding-left: 16px;
          }
          .money {
            color: #333;
            font-size: 20px;
            font-weight: 700;
            line-height: normal;
            min-width: 100px;
            white-space: nowrap;
            span {
              font-size: 25px;
              font-weight: 700;
              line-height: normal;
            }
          }
          .text {
            color: #666;
            font-size: 14px;
            font-weight: 500;
            line-height: normal;
            white-space: nowrap;
            position: relative;
            span {
              height: 16px;
              line-height: 16px;
              display: inline-block;
              text-align: center;
              background-color: #ff671b;
              border-radius: 16px 16px 16px 0px;
              color: #fff;
              padding: 0 4px;
              position: absolute;
              bottom: 7px;
            }
          }
        }
      }
    }
    .item_foo {
      color: #999dad;
      font-size: 14px;
      font-weight: 400;
      margin-top: 10px;
      padding-left: 8px;
    }
  }
  .footer_box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 6px;
    .l {
      display: flex;
      align-items: center;
      flex: auto;
      img {
        width: 28px;
        height: 28px;
        margin-right: 10px;
      }
      .card_name {
        color: #333;
        font-size: 15px;
        font-weight: 500;
        line-height: normal;
        margin-right: 10.5px;
      }
      .card_info {
        color: #999dad;
        font-size: 14px;
        font-weight: 400;
      }
    }
  }
}
.apply-btn {
  // bottom: none;
  border: none;
  width: auto;
  height: auto;
  padding: 0;
}
</style>
