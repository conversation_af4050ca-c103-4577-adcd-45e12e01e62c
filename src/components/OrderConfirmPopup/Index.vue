<template>
  <van-popup
    v-model:show="show"
    round
    position="bottom"
    class="order-confirm-popup"
    @closed="onClosed"
  >
    <div class="order-confirm-page">
      <div class="order-confirm-page-context">
        <GoodsInfo
          :spuInfo="spuInfo"
          :goodsSku="goodsSku"
          :goodsSpecData="goodsSpecData"
          :skuNum="skuNum"
        />
        <div class="scroll-wrapper">
          <template v-if="orderInfo.categoryGroup === 'GENERAL'">
            <!-- 支持物流 -->
            <div v-if="addressInfo.id" class="address-info" @click="changeAddress">
              <img
                style="width: 30px; height: 30px; margin-right: 12px"
                src="@/assets/icons/地址定位.png"
              />
              <div class="content">
                <div class="name-phone">
                  <div class="name">{{ addressInfo.contactName }}</div>
                  <div class="phone">{{ addressInfo.contactTel }}</div>
                </div>
                <div class="address overflow-1">
                  {{
                    addressInfo.province +
                    addressInfo.city +
                    addressInfo.district +
                    addressInfo.address
                  }}
                </div>
              </div>
              <van-icon name="arrow" color="#CBCBCB" :size="14" />
            </div>
            <div v-else class="address-info-empty" @click="changeAddress">
              <div class="content">
                <img src="@/assets/images/goods/empty-address-icon.png" />
                <div class="text">请选择收货地址</div>
              </div>
              <van-icon class="arrow-icon" color="#CBCBCB" :size="14" name="arrow" />
            </div>
          </template>
          <div
            v-else-if="
              orderInfo.categoryGroup === 'DIRECT_ADD' || orderInfo.categoryGroup === 'TELE'
            "
            class="direct-wrapper"
          >
            <van-cell-group inset>
              <van-field
                v-model="virtualSubmitMsg.chargeAccount"
                label="充值账号"
                placeholder="请输入充值账号"
              />
            </van-cell-group>
          </div>
          <GoodsSpec
            :spuInfo="spuInfo"
            v-model:skuNum="skuNum"
            :goodsSpecData="goodsSpecData"
            :goodsSku="goodsSku"
            :skuList="skuList"
            @updateSpecSku="onUpdateSpecSku"
            @updateSkuNum="onUpdateSkuNum"
          />
          <!-- <div class="goods-info">
          <order-goods-row :goods-list="orderInfo.msOrderSpus" />
        </div> -->
          <div class="order-total">
            <div class="order-tell">
              <div class="label">商品总价</div>
              <div class="label">￥{{ orderInfo.totalAmount }}</div>
            </div>
            <div class="order-tell" v-if="orderInfo.mbAmount">
              <div class="label">会员优惠</div>
              <div class="label text-red">-￥{{ orderInfo.mbAmount }}</div>
            </div>
            <div class="order-tell" v-if="orderInfo.tvAmount">
              <div class="label">优惠券抵扣</div>
              <div class="label text-red">-￥{{ orderInfo.tvAmount }}</div>
            </div>
            <div class="order-tell" v-if="orderInfo.categoryGroup === 'GENERAL'">
              <div class="label">运费</div>
              <div class="label">￥0</div>
            </div>
          </div>
          <coupon-container :order-info="orderData" @showCoupon="showCoupon" />
          <div class="order-total" v-if="orderInfo.shoppingFlag === 'I'">
            <div class="order-tell">
              <div class="label">
                使用积分<span class="text-gray text-sm"
                  >(可用积分<span class="text-red">{{
                    orderInfo.integralInfo.deductionIntegral
                  }}</span
                  >, 剩余积分<span class="text-red">{{
                    orderInfo.integralInfo.availableIntegral
                  }}</span
                  >)</span
                >
              </div>
              <div class="label text-red">
                <van-stepper
                  v-model="useIntegral"
                  @change="changeIntegral"
                  :max="orderInfo.integralInfo.deductionIntegral"
                  step="100"
                  button-size="20px"
                  input-width="44px"
                  :disabled="orderInfo.integralInfo.integralType === 'ALL' ? true : false"
                />
              </div>
            </div>
            <div class="order-tell">
              <div class="label">积分抵扣</div>
              <div class="label text-red">-￥{{ orderInfo.integralInfo.deductionAmount }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="footer" :class="{ 'iphonex-bottom': isIphoneX }">
        <span class="footer-total"
          >合计：<span class="amountT">￥</span><span class="amount">{{ orderInfo.payAmount }}</span></span
        >
        <div class="btn theme-linear-gradient" @click="handlePay">
          {{ orderInfo.payAmount > 0 ? '去付款' : '确认' }}
        </div>
      </div>
      <coupon-select
        :use-coupon="orderData.tickets"
        ref="couponSelectRef"
        @resetOrder="orderParams"
      />
      <img class="close-img" @click="onClickBack" src="@/assets/images/goods/colse.png" />
    </div>
    <SelectAddressPopup
      ref="selectAddressPopupRef"
      @choose="onChooseAddress"
      @update="onUpdateAddress"
      @delete="onDeleteAddress"
    />
  </van-popup>
</template>

<script setup>
// import OrderGoodsRow from '@/components/OrderGoodsRow'
import { goodsPreOrder, saveGoodsOrder } from '@/api/goods-order'
import CouponContainer from './components/CouponContainer'
import CouponSelect from './components/CouponSelect'
import { divide } from '@/utils/common'
import { showToast, showConfirmDialog } from 'vant'
import GoodsInfo from './components/GoodsInfo.vue'
import GoodsSpec from './components/GoodsSpec.vue'
import SelectAddressPopup from '../SelectAddressPopup/SelectAddressPopup.vue'
const isIphoneX = window.isIphoneX
const emit = defineEmits(['updateSpecSku', 'updateSkuNum', 'addressChanged'])
const { proxy } = getCurrentInstance()
const router = useRouter()
const cheight = ref('600px')
const useIntegral = ref(0)
const couponSelectRef = ref(null)
const show = ref(false)
const selectAddressPopupRef = ref()
const data = reactive({
  confirmData: {},
  orderData: {},
  orderInfo: {},
  addressInfo: {},
  chooseAddressInfo: {},
  virtualSubmitMsg: {
    chargeAccount: '',
  },
  checkedCoupon: {},
})
const {
  confirmData,
  orderData,
  orderInfo,
  addressInfo,
  chooseAddressInfo,
  virtualSubmitMsg,
  checkedCoupon,
} = toRefs(data)
const spuInfo = ref({})
const goodsSku = ref({})
const goodsSpecData = ref([])
const skuList = ref([])
const skuNum = ref(1)

provide('checkedCoupon', checkedCoupon)
// 更新选中优惠券
const updateCoupon = (item) => {
  checkedCoupon.value = item
}
provide('updateCoupon', updateCoupon)

onMounted(() => {
  if (window.screen.height > 667) {
    cheight.value = '700px'
  }
})

const changeAddress = () => {
  selectAddressPopupRef.value.open()
}

//  积分修改
const changeIntegral = (val) => {
  if (val) {
    const deductionAmount = divide(val, orderInfo.value.integralInfo.integralRate)
    orderInfo.value.integralInfo.deductionAmount = deductionAmount
    orderInfo.value.payAmount =
      orderInfo.value.totalAmount - orderInfo.value.tvAmount - deductionAmount
  }
}

// 提交订单
const handlePay = () => {
  const msOrder = {
    categoryGroup: orderInfo.value.categoryGroup,
    totalAmount: orderInfo.value.totalAmount,
    payAmount: orderInfo.value.payAmount,
    point: orderInfo.value.shoppingFlag === 'I' ? useIntegral.value : 0,
    ...confirmData.value,
    msOrderSpus: {
      quantity: skuNum.value,
      spuId: spuInfo.value.spuId,
      skuId: goodsSku.value.skuId,
    },
    addressInfo: addressInfo.value,
  }
  if (orderInfo.value.categoryGroup === 'GENERAL') {
    // 需要物流
    if (addressInfo.value.id) {
      msOrder['addressId'] = addressInfo.value.id
      submitOrder(msOrder)
    } else {
      showToast('请选择地址')
    }
  } else if (
    orderInfo.value.categoryGroup === 'DIRECT_ADD' ||
    orderInfo.value.categoryGroup === 'TELE'
  ) {
    if (virtualSubmitMsg.value.chargeAccount) {
      msOrder['virtualSubmitMsg'] = virtualSubmitMsg.value
      submitOrder(msOrder)
    } else {
      showToast('请正确填写充值账号')
    }
  } else {
    submitOrder(msOrder)
  }
}

// 提交订单
const submitOrder = (msOrder) => {
  if (orderData.value.tickets.length > 0 && !checkedCoupon.value.ticketNo) {
    showConfirmDialog({
      title: '提示',
      message: '您有可用优惠券未选择，确认不使用吗？',
    })
      .then(() => {
        orderConfirm(msOrder)
      })
      .catch(() => {
        // on cancel
      })
  } else {
    orderConfirm(msOrder)
  }
}

const orderConfirm = (msOrder) => {
  saveGoodsOrder({ msOrder }).then((res) => {
    if (res.data.cashier === 'Y') {
      const orderId = res.data.orderId
      router.push({ path: '/cashier', query: { orderId: orderId } })
    } else {
      const orderId = res.data.orderId
      router.replace({ name: 'MyGoodsOrderDetail', query: { orderId: orderId } })
    }
  })
}
const showCoupon = () => {
  couponSelectRef.value.show = true
}

// 下单参数
const orderParams = () => {
  confirmData.value = JSON.parse(localStorage.getItem(proxy.$global.PARAM_ORDERCONFIRM))
  if (checkedCoupon.value?.ticketNo) {
    // 带上优惠券
    confirmData.value.ticketNo = checkedCoupon.value.ticketNo
  }
  const extraInfo = JSON.parse(localStorage.getItem(proxy.$global.PARAM_ORDERCONFIRM_EXTRA_INFO))
  spuInfo.value = extraInfo.spuInfo
  goodsSku.value = extraInfo.goodsSku
  goodsSpecData.value = extraInfo.goodsSpecData
  skuNum.value = extraInfo.skuNum
  skuList.value = extraInfo.skuList
  preOrderSubmit()
}

const preOrderSubmit = () => {
  const params = {
    msOrder: {
      ...confirmData.value,
      msOrderSpus: {
        quantity: skuNum.value,
        spuId: spuInfo.value.spuId,
        skuId: goodsSku.value.skuId,
      },
      addressInfo: addressInfo.value,
    },
  }
  console.log('预下单参数：', params)
  goodsPreOrder(params).then((res) => {
    orderInfo.value = res.data.msOrder
    orderData.value = res.data
    const checkedAddress = sessionStorage.getItem(proxy.$global.ADDRESS_CHECKED)
    if (checkedAddress) {
      // 选择地址返回
      addressInfo.value = JSON.parse(checkedAddress)
      sessionStorage.removeItem(proxy.$global.ADDRESS_CHECKED)
    } else if (
      confirmData.value.addressInfo &&
      confirmData.value.addressInfo.id &&
      !checkedAddress
    ) {
      // 选择商品并选择地址进入（chooseAddressInfo是用来储存最新已选地址的，当重新选择了地址后，赋值最新的选择地址）
      addressInfo.value = chooseAddressInfo.value.addressInfo
        ? chooseAddressInfo.value.addressInfo
        : confirmData.value.addressInfo
        ? confirmData.value.addressInfo
        : {}
    } else {
      // 再次购买
      console.log('res.data.msOrder.deliveryAddress', res.data.msOrder.deliveryAddress)
      addressInfo.value = res.data.msOrder.deliveryAddress ? res.data.msOrder.deliveryAddress : {}
    }
    if (!res.data.msOrder.deliveryAddress) {
      // 删除地址返回订单页
      console.log('删除地址返回订单页', res.data.msOrder.deliveryAddress)
      addressInfo.value = {}
    }
    if (orderInfo.value.shoppingFlag === 'I') {
      // 积分抵扣商品默认积分
      useIntegral.value = orderInfo.value.integralInfo.deductionIntegral
    }
  })
}

const open = () => {
  show.value = true
  orderParams()
}

const onClickBack = () => {
  console.log('点击关闭')
  show.value = false
}

const onClosed = () => {
  chooseAddressInfo.value = {}
  console.log('关闭')
}

// 更新规格/sku
const onUpdateSpecSku = (spec, sku) => {
  goodsSpecData.value = spec
  goodsSku.value = sku
  console.log('规格改变，重新预下单')
  emit('updateSpecSku', spec, sku)
  // 地址变更，重新预下单
  preOrderSubmit()
}

const onUpdateSkuNum = (val) => {
  console.log('购买数量改变，重新预下单')
  emit('updateSkuNum', val)
  // 地址变更，重新预下单
  preOrderSubmit()
}

const onChooseAddress = (item) => {
  // addressInfo.value = item
  chooseAddressInfo.value.addressInfo = item
  emit('addressChanged', JSON.parse(JSON.stringify(item)))
  console.log('收获地址改变，重新预下单')
  // 地址变更，重新预下单
  preOrderSubmit()
}

const onUpdateAddress = (item) => {
  console.log('地址被修改了', item)
  emit('addressChanged', JSON.parse(JSON.stringify(item)))
  if (addressInfo.value.id === item.id) {
    addressInfo.value = item
  }
}

const onDeleteAddress = (id) => {
  console.log('地址被删除了', id)
  emit('addressChanged', {})
  if (addressInfo.value.id === id) {
    addressInfo.value = {}
  }
}

defineExpose({
  open,
})
</script>

<style lang="scss" scoped>
.order-confirm-page {
  // height: 100%;
  width: 100vw;
  width: 100dvw;
  display: flex;
  flex-direction: column;
  // position: absolute;
  background-color: #f6f6f6;
  border-radius: 10px 10px 0 0;
  max-height: 80vh;
  max-height: 80dvh;

  .close-img {
    position: absolute;
    top: 6px;
    right: 6px;
    width: 24px;
    height: 24px;
    padding: 10px;

    &:active {
      color: red;
    }

    &:hover {
      color: yellow;
    }
  }

  &-context {
    flex-grow: 1;
    overflow: hidden;
    overflow-y: auto;
    position: relative;

    .address-info {
      margin: 10px;
      padding: 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: #ffffff;
      border-radius: 4px;

      .content {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 4px;
      }

      .name-phone {
        display: flex;
        font-size: 14px;
        font-weight: 400;
        color: #333;

        .name {
          margin-right: 6px;
        }

        .phone {
          color: #999;
        }
      }

      .address {
        font-size: 14px;
        font-weight: 400;
        color: #333;
        line-height: 22px;
      }
    }

    .address-info-empty {
      margin: 10px;
      padding: 19px 0 22px 0;
      position: relative;
      background: #ffffff;
      border-radius: 4px;

      .content {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;

        img {
          width: 48px;
          height: 48px;
          display: block;
        }

        font-size: 13px;
        font-weight: 400;
        color: #999999;
        line-height: 22px;
      }

      .arrow-icon {
        position: absolute;
        right: 16px;
        top: calc(50% - 10px);
      }
    }

    .order-total {
      margin: 10px;
      padding: 0 16px;
      border-radius: 4px;
      background: #ffffff;

      .order-tell {
        display: flex;
        justify-content: space-between;
        padding: 14px 0;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #222222;
        line-height: 22px;
      }
    }
  }
}

.footer {
  background: #ffffff;
  padding: 10px 16px;
  // position: fixed;
  bottom: 0;
  width: calc(100% - 32px);
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: content-box;
  padding-bottom: calc(10px + var(--safe-area-inset-bottom));
  margin-top: auto;

  .footer-total {
    font-size: 15px;
    display: flex;
    align-items: center;
    .amountT{
      color: #F43727;
      font-weight: bold;
    }

    .amount {
      font-size: 20px;
      font-family: PingFang-SC-Bold, PingFang-SC;
      font-weight: bold;
      line-height: 22px;
      color: #F43727;
    }
  }

  .btn {
    width: 100px;
    border-radius: 7px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    font-size: 14px;
    color: #ffffff;
    margin-left: 11px;
  }
}

.footer.iphonex-bottom {
  // padding-bottom: 51px !important;
  // padding-bottom: calc(10px + var(--safe-area-inset-bottom));
}

:deep(.van-cell-group--inset) {
  margin: 10px;
}

.order-confirm-popup {
  // height: 100%;
}
.order-confirm-page-context {
  flex: 1;

  display: flex;
  flex-direction: column;
  height: 100%;
}
.scroll-wrapper {
  flex: 1;
  min-height: 0;
  overflow-y: auto;
}
</style>
