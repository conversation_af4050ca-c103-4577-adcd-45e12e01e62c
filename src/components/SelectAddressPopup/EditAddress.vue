<template>
  <van-popup
    v-model:show="visible"
    round
    position="bottom"
    :style="{ height: cheight, 'overflow-y': 'unset' }"
    teleport="body"
    @closed="onClosed"
  >
    <div class="my-address-form-page">
      <div class="title">{{ title }}</div>
      <img class="close-img" @click="onClickBack" src="@/assets/images/goods/colse.png" />
      <div class="my-address-form-page-context" :class="{ 'iphonex-bottom': isIphoneX }">
        <van-form @submit="onSubmit">
          <van-cell-group inset>
            <van-field
              v-model="addressInfo.contactName"
              name="contactName"
              label="收货人"
              placeholder="请填写收货人"
              :rules="[{ required: true, message: '请填写收货人' }]"
            />
            <van-field
              v-model="addressInfo.contactTel"
              name="contactTel"
              label="手机号码"
              type="tel"
              placeholder="请填写手机号码"
              :rules="[{ required: true, validator: validatorPhone, message: '请填写手机号码' }]"
            />
            <van-field
              v-model="addressInfo.areaData"
              is-link
              readonly
              label="所在地址"
              placeholder="点击选择省市区"
              :rules="[{ validator: validArea, message: '请选择省市区' }]"
              @click="showArea = true"
            />
            <van-field
              label="详情地址"
              v-model.trim="addressInfo.address"
              placeholder="请补充详细地址 如：XX路/街道XX号XX小区XX栋XX"
              type="textarea"
              :rules="[{ required: true, message: '请填写详细地址' }]"
            />
          </van-cell-group>
          <div class="swith-warpper">
            <div class="label">
              <div class="title">设为默认地址</div>
              <div class="tips">提醒：每次下单会默认推荐使用该地址</div>
            </div>
            <van-switch v-model="checked" />
          </div>
          <div class="footer" :class="{ 'iphonex-bottom': isIphoneX }">
            <van-button class="addr-btn" round block type="primary" native-type="submit"
              >保存</van-button
            >
          </div>
        </van-form>
      </div>
      <van-popup v-model:show="showArea" position="bottom">
        <van-area
          v-model="addressInfo.districtCode"
          :area-list="areaList"
          @confirm="onAreaConfirm"
          @cancel="showArea = false"
        />
      </van-popup>
    </div>
  </van-popup>
</template>

<script setup>
import { areaList } from '@vant/area-data'
import { validPhone } from '@/utils/common'
import { addAddress, updateAddress } from '@/api/address'

const emit = defineEmits(['added', 'edited'])

const { proxy } = getCurrentInstance()
const isIphoneX = window.isIphoneX
const checked = ref(false)
const title = ref('')
const cheight = ref('600px')
const visible = ref(false)

const data = reactive({
  addressInfo: {},
})
const { addressInfo } = toRefs(data)
const validArea = (val) => (val ? true : false)
const validatorPhone = (val) => {
  if (!validPhone(val)) {
    return '请填写正确的手机号码'
  }
}
const showArea = ref(false)
const onAreaConfirm = (areaValues) => {
  addressInfo.value.areaData = areaValues.selectedOptions
    .filter((item) => !!item)
    .map((item) => item.text)
    .join('/')
  addressInfo.value.province = areaValues.selectedOptions[0].text
  addressInfo.value.city = areaValues.selectedOptions[1].text
  addressInfo.value.district = areaValues.selectedOptions[2].text
  addressInfo.value.districtCode = areaValues.selectedOptions[2].value
  showArea.value = false
}
// 提交
const onSubmit = () => {
  addressInfo.value.isDefault = checked.value ? 'Y' : 'N'
  const address = JSON.parse(JSON.stringify(addressInfo.value))
  if (addressInfo.value.id) {
    updateAddress({ address }).then((res) => {
      proxy.onToastSucc(() => {
        emit('edited', JSON.parse(JSON.stringify(address)))
        onClickBack()
      }, '编辑成功！')
    })
  } else {
    addAddress({ address: addressInfo.value }).then((res) => {
      proxy.onToastSucc(() => {
        emit('added', JSON.parse(JSON.stringify(address)))
        onClickBack()
      }, '新增成功！')
    })
  }
}
const open = (item = null) => {
  visible.value = true

  if (item) {
    addressInfo.value = item
    checked.value = addressInfo.value.isDefault === 'Y' ? true : false
    addressInfo.value.areaData =
      addressInfo.value.province + '/' + addressInfo.value.city + '/' + addressInfo.value.district
    title.value = '编辑地址'
  } else {
    addressInfo.value = {}
    title.value = '新增地址'
  }
}

const onClosed = () => {}

const onClickBack = () => {
  visible.value = false
}

onMounted(() => {
  if (window.screen.height > 667) {
    cheight.value = '700px'
  }
})

defineExpose({
  open,
})
</script>

<style lang="scss" scoped>
.my-address-form-page {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  position: absolute;
  background-color: #f6f6f6;
  border-radius: 10px 10px 0 0;

  .title {
    font-size: 15px;
    font-weight: bold;
    color: #000;
    text-align: center;
    margin: 20px 0 10px 0;
  }

  .close-img {
    position: absolute;
    top: 6px;
    right: 6px;
    width: 24px;
    height: 24px;
    padding: 10px;
  }

  &-context {
    flex-grow: 1;
    overflow: hidden;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    position: relative;

    .swith-warpper {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: #ffffff;
      border-radius: 8px;
      margin: 10px;
      padding: 14px 16px;

      .title {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #222222;
        line-height: 22px;
        text-align: left;
      }

      .tips {
        font-size: 12px;
        transform: scale(0.85);
        transform-origin: left top;
        color: #666666;
        line-height: 14px;
      }
    }
  }
}

.van-cell-group--inset {
  margin: 10px;
}

.footer {
  background: #ffffff;
  padding: 7px 16px;
  position: fixed;
  bottom: 0;
  width: calc(100% - 32px);

  .addr-btn {
    border-radius: 8px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    font-size: 14px;
    color: #ffffff;
    border: none;
    // background: linear-gradient(180deg, #FF671A 0%, #FF4019 100%);
    background: var(--primary-linear-to-bottom);
  }
}

.footer.iphonex-bottom {
  // padding-bottom: 51px !important;
  padding-bottom: calc(7px + var(--safe-area-inset-bottom));
}
</style>
