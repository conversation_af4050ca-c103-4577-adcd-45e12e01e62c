<template>
  <van-popup
    v-model:show="visible"
    round
    position="bottom"
    class="select-address-popup"
    teleport="body"
    @closed="onClosed"
  >
    <div class="my-address-page">
      <div class="title">选择收货地址</div>
      <img class="close-img" @click="onClickBack" src="@/assets/images/goods/colse.png" />
      <div class="my-address-page-context">
        <div class="list">
          <div class="item" v-for="(item, index) in addressList" :key="index">
            <div class="info" @click="handleChoose(item)">
              <div class="name-phone">
                <div class="name">{{ item.contactName }}</div>
                <div class="phone">{{ item.contactTel }}</div>
              </div>
              <div class="content">
                {{ item.province + item.city + item.district + item.address }}
              </div>
            </div>
            <div class="action">
              <div class="action-left" @click="handleDefault(item)">
                <!-- <img v-if="item.isDefault === 'Y'" src="@/assets/images/cashier/active.png">
                <img v-else src="@/assets/images/cashier/inactive.png"> -->
                <van-checkbox :model-value="item.isDefault === 'Y'" />
                <span>默认地址</span>
              </div>
              <div class="action-right">
                <div class="btn" @click="handleUpdate(item)">编辑</div>
                <div class="btn" @click="handleRemove(item)">删除</div>
              </div>
            </div>
          </div>
        </div>
        <div class="footer">
          <div class="btn theme-linear-gradient" @click="handleUpdate()"><van-icon name="plus" /> 新增收货地址</div>
        </div>
      </div>
      <van-popup round v-model:show="show">
        <div class="warning-popup">
          <div class="title">确定删除收货地址？</div>
          <div class="content">
            <div class="btn cancel text-black" @click="show = false">取消</div>
            <div class="btn theme-linear-gradient text-white" @click="submitDelete">确认</div>
          </div>
        </div>
      </van-popup>
    </div>
    <EditAddress ref="editAddressRef" @added="onAdded" @edited="onEdited" />
  </van-popup>
</template>

<script setup>
import { listAddress, updateDefault, deleteAddress } from '@/api/address'
import EditAddress from './EditAddress.vue'

const isIphoneX = window.isIphoneX
const { proxy } = getCurrentInstance()
const visible = ref(false)
const addressList = ref([])
const show = ref(false)
const deleId = ref(null)
const editAddressRef = ref()

const emit = defineEmits(['choose', 'update', 'delete'])

const props = defineProps({
  type: {
    type: String,
    default: '1',
  },
})

const onClickBack = () => {
  visible.value = false
}

// 更新默认
const handleDefault = (item) => {
  if (item.isDefault !== 'Y') {
    updateDefault({ id: item.id }).then((res) => {
      proxy.onToastSucc(() => {
        getList()
      }, '更新成功！')
    })
  }
}

// 删除地址
const handleRemove = (item) => {
  deleId.value = item.id
  show.value = true
}

const submitDelete = () => {
  deleteAddress({ id: deleId.value }).then((res) => {
    show.value = false
    emit('delete', deleId.value)
    proxy.onToastSucc(() => {
      getList()
    }, '删除成功！')
  })
}

// 选择
const handleChoose = (item) => {
  emit('choose', JSON.parse(JSON.stringify(item)))
  onClickBack()
}

// 新增编辑
const handleUpdate = (item) => {
  editAddressRef.value.open(item)
}

const getList = () => {
  listAddress({ pageNum: 1, pageSize: 100 }).then((res) => {
    addressList.value = res.data
  })
}

const open = () => {
  visible.value = true
  getList()
}

const onClosed = () => {
  // 关闭内部弹窗
  show.value = false
}

const onAdded = (item) => {
  getList()
}

const onEdited = (item) => {
  emit('update', item)
  getList()
}

defineExpose({
  open,
})
</script>

<style lang="scss" scoped>
.select-address-popup {
  height: 60%;
  max-height: 80%;
}
.my-address-page {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  position: absolute;
  background-color: #f6f6f6;
  border-radius: 10px 10px 0 0;

  .title {
    font-size: 15px;
    font-weight: bold;
    color: #000;
    text-align: center;
    margin: 20px 0 10px 0;
  }

  .close-img {
    position: absolute;
    top: 6px;
    right: 6px;
    width: 24px;
    height: 24px;
    padding: 10px;
  }

  &-context {
    flex-grow: 1;
    overflow: hidden;
    overflow-y: scroll;
    display: flex;
    flex-direction: column;
    position: relative;

    .list {
      flex: 1;
      .item {
        margin: 10px;
        background: #ffffff;
        border-radius: 8px;

        .info {
          padding: 16px;
          padding-bottom: 9px;
          border-bottom: 1px solid #ededee;

          .name-phone {
            display: flex;
            font-size: 16px;
            font-weight: bold;
            color: #222222;

            .name {
              margin-right: 20px;
            }
          }

          .content {
            font-size: 13px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #666666;
            line-height: 22px;
            margin-top: 6px;
          }
        }

        .action {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 9px 16px 14px;

          &-left {
            display: flex;
            align-items: center;
            font-size: 12px;
            font-weight: bold;
            color: #222222;

            .van-checkbox {
              // width: 18px;
              // height: 18px;
              --van-checkbox-size: 18px;
              margin-right: 7px;
            }
          }

          &-right {
            display: flex;
            align-items: center;
            font-size: 12px;
            color: #222222;

            .btn {
              margin-left: 20px;
            }
          }
        }
      }
    }

    .footer {
      background: #ffffff;
      padding: 7px 16px;
      padding-bottom: calc(7px + var(--safe-area-inset-bottom));
      position: sticky;
      bottom: 0;
      width: calc(100% - 32px);

      .btn {
        border-radius: 8px;
        height: 40px;
        line-height: 40px;
        text-align: center;
        font-size: 14px;
        color: #ffffff;
      }
    }
  }
}

.warning-popup {
  width: 235px;
  background: #ffffff;

  .title {
    text-align: center;
    margin-top: 19px;
    font-size: 16px;
    font-family: PingFang-SC-Bold, PingFang-SC;
    font-weight: bold;
    color: #222222;
    line-height: 22px;
  }

  .content {
    display: flex;
    justify-content: space-between;
    margin: 27px 20px 16px;

    .btn {
      width: 90px;
      height: 32px;
      border-radius: 16px;
      line-height: 32px;
      text-align: center;
      font-size: 14px;
    }

    .btn.cancel {
      background: #eeeeee;
    }
  }
}
</style>
