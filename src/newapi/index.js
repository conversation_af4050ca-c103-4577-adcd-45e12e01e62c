import { createAlova } from 'alova'
import adapterFetch from 'alova/fetch'

import VueHook from 'alova/vue'
import { createServerTokenAuthentication } from 'alova/client'

import { getToken, setToken, removeToken, getAppVersion, getUserInfo } from '@/utils/auth'
import ua from '@/utils/ua'
import deviceId from '@/utils/deviceId'




const { onAuthRequired, onResponseRefreshToken } = createServerTokenAuthentication({
  // ...
  assignToken(method) {
    console.log('assignToken', method)
    method.config.headers.appToken = getToken() ?? ''
  },
  refreshTokenOnSuccess: {
    async isExpired(response, method) {
      console.log('isExpired', response, method)

      const data = await response.json()
      console.log(data)

      return false
    },
    handler(response, method) {
      console.log('handler', response, method)
      throw new Error('登录已失效，请重新登录')
    },
  },
})

const alovaInstance = createAlova({
  baseURL: import.meta.env.VITE_APP_BASE_API,
  timeout: 30000,
  // cacheFor: {},
  statesHook: VueHook,
  requestAdapter: adapterFetch(),
  beforeRequest: onAuthRequired((method) => {
    console.log('beforeRequest', method)
    method.config.headers['Device-Id'] = deviceId ?? ''
    method.config.headers['Browser'] = ua.browser ?? ''
    method.config.headers['CPU'] = ua.cpu ?? ''
    method.config.headers['Device'] = ua.device ?? ''
    method.config.headers['Engine'] = ua.engine ?? ''
    method.config.headers['OS'] = ua.os ?? ''
  }),
  responded: onResponseRefreshToken((response, method) => {
    console.log(response)

    //...原响应成功拦截器
    return response.json()
  }),
})

export default alovaInstance
