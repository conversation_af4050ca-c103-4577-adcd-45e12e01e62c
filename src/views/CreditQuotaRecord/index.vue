<template>
  <section class="page">
    <header>
      <NavigationBar title="额度记录" />
    </header>
    <main>
      <van-pull-refresh v-model="refreshing" @refresh="refresh(0)">
        <!-- <div>ConsumeAmountRecord</div> -->
        <div class="main-card">
          <div class="main-card__title">总额度</div>
          <div class="main-card__amount">
            <sub>￥</sub>
            <span>{{
              formatNumber({
                padRight: 2,
              })(quotaInfo?.consumAmount ?? 0)
            }}</span>
          </div>
        </div>
        <van-list
          :immediate-check="false"
          :loading="listIsLoading"
          :error="listError"
          :finished="list.length >= total"
          :error-text="listError?.message"
          finished-text="—— 没有更多了 ——"
          @load="onLoad"
        >
          <div class="list-card">
            <van-cell-group :border="false" title="">
              <van-cell
                v-for="(item, index) in list"
                :key="index"
                :label="dayjs(item.accountTime).format('YYYY-MM-DD HH:mm:ss')"
                :title="item.abstractInfo"
                :value="
                  formatNumber({
                    padRight: 2,
                    prefix: (item.crdrFlag === 'DEBIT' ? '+' : '-') + '￥',
                  })(item.amount)
                "
              />
            </van-cell-group>
          </div>
        </van-list>
      </van-pull-refresh>
    </main>
  </section>
</template>

<script setup>
defineOptions({
  name: 'CreditQuotaRecord',
})
import NavigationBar from '@/components/NavigationBar/index2.vue'
import { useAsyncState, useToggle, whenever } from '@vueuse/core'
import { creditQuota, saveContacts, getCreditQuotaRecord } from '@/api/customer'
import global from '@/constant/Global.js'
import formatNumber from 'format-number'
import { useRouteParams, useRouteQuery } from '@vueuse/router'
import { useStore } from 'vuex'
import { useRoute, useRouter } from 'vue-router'
import dayjs from 'dayjs'

const router = useRouter()
const route = useRoute()
const store = useStore()
const user = computed(() => store.getters.userInfo)

const sceneCode = useRouteParams('sceneCode', '')

import { useRequest } from 'alova/client'
import alovaInstance from '@/newapi'

const { data } = useRequest(
  alovaInstance.Post('/gateway', {
    apiCode: 'BL101',
    requestType: 'query',
    sceneCode: sceneCode.value,
  }),
  {
    immediate: true,
  }
)

// const [refreshing, toggleRefreshing] = useToggle(false)

// async function onRefresh() {
//   await refreshQuota()
//   toggleRefreshing(false)
// }
// async function refresh() {}

const {
  state: quotaInfo,
  isLoading: quotaIsLoading,
  execute: refreshQuota,
  isReady: quotaIsReady,
} = useAsyncState(
  async () => {
    if (!user.value?.id) return null
    if (user.value.creditIntentionFlag !== 'Y') return null
    const { data } = await creditQuota(
      {
        requestType: 'query',
        sceneCode: sceneCode.value,
      },
      false
    )
    return data
  },
  null,
  {
    resetOnExecute: false,
    onSuccess(data) {
      console.log(data)
    },
  }
)

const list = ref([])
const page = ref(1)

const {
  state: total,
  execute: getList,
  isLoading: listIsLoading,
  error: listError,
} = useAsyncState(
  async (isReload = false) => {
    // console.log(isReload, page.value)

    const { data, total } = await getCreditQuotaRecord({
      pageSize: 10,
      pageNum: isReload ? 1 : ++page.value,
      productId: quotaInfo.value?.productId,
    })
    // console.log(data)
    if (isReload) {
      list.value = data
      page.value = 1
    } else {
      list.value.push(...data)
    }

    // list.value = Array(20).fill()

    // console.log(page.value)

    // if (data.length === 10) {
    //   return Infinity
    // }

    // return list.value.length
    return total
  },
  null,
  {
    immediate: !!quotaInfo.value?.productId,
    resetOnExecute: false,
  }
)

whenever(
  () => quotaInfo.value?.productId,
  () => {
    getList(0, true)
  }
)

const { execute: refresh, isLoading: refreshing } = useAsyncState(
  async () => {
    console.log('刷新')
    await refreshQuota()
    await getList(0, true)
  },
  null,
  {
    immediate: false,
  }
)

async function onLoad() {
  console.log('onLoad')

  await getList()
}
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  font-size: 14px;
  line-height: 1.2;
  color: #000;
  header {
    flex: none;
    background: #fff;
  }
  main {
    flex: 1;
    height: auto;
    background: #f6f6f6;
    overflow-y: scroll;
    overflow-x: hidden;
    .van-pull-refresh {
      min-height: 100%;
      // :deep(.van-pull-refresh__track) {
      //   min-height: 100%;
      // }
    }
    padding-bottom: var(--safe-area-inset-bottom);
  }
}

.main-card {
  border-radius: 12px;
  border: 5px solid #fff;
  background: linear-gradient(1deg, rgba(255, 255, 255, 0) 50.16%, rgba(70, 113, 235, 0.2) 99.48%);
  // width: 355px;
  // height: 174px;
  margin: 20px 10px 0;
  padding: 20px 24px;
  box-sizing: border-box;
  // position: sticky;
  // top: 20px;
  &__title {
    font-size: 18px;
  }
  &__amount {
    margin-top: 4px;
    font-size: 34px;
    font-weight: 700;
    font-family: 'DIN';
    sub {
      font-size: 0.8em;
    }
    * {
      font: inherit;
    }
  }
}
.list-card {
  background: #fff;
  border-radius: 12px;
  margin: 12px 10px;
  overflow: hidden;
  // padding: 12px;
  .van-cell {
    padding: 10px 20px;
    :deep(.van-cell__title) {
      color: #000;
      font-size: 15px;
    }
    :deep(.van-cell__label) {
      font-size: 14px;
      color: #00000066;
    }
    :deep(.van-cell__value) {
      width: 100px;
      flex: none;
      color: #000;
    }
  }
}
</style>
