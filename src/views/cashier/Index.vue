<template>
  <div class="cashier-page">
    <navigation-bar pageName="收银台" @onLeftClick="retainShow = true"></navigation-bar>
    <div class="cashier-page-context" :class="{ 'iphonex-bottom': isIphoneX }">
      <van-pull-refresh
        v-model="loading"
        :disabled="pullRefreshDisabled"
        @refresh="onRefresh"
        style="flex: 1"
      >
        <div class="amount-time">
          <div class="amount">￥{{ amount }}</div>
          <div class="time">支付剩余时间：<van-count-down :time="time" /></div>
        </div>
        <div class="pay-list-wrapper" @scroll="onPayListScroll">
          <div class="pay-list">
            <div class="pay-item" v-for="item in payList" :key="item.id">
              <!-- 新消费分期 -->
              <instalment-pay
                ref="instalmentRef"
                v-if="item.payType === $global.PAY_TYPE_BILLPAY"
                v-model="paycheck"
                :order-id="orderId"
                :pay-info="item"
                :bill-flag="billFlag"
                @updatePayProduct="updatePayProduct"
                @updatePeriods="updatePeriods"
                @updateSubProduct="updateSubProduct"
              />
              <bank-card-pay
                v-else-if="item.payType === $global.PAY_TYPE_QUICKPAY"
                v-model="paycheck"
                :pay-info="item"
                :order-id="orderId"
                @updatePayProduct="updatePayProduct"
                @updateCheckBank="updateCheckBank"
              />
              <pay-later
                v-else-if="item.payType === $global.PAY_TYPE_PAYLATER"
                v-model="paycheck"
                :pay-info="item"
                :order-id="orderId"
                @updatePayProduct="updatePayProduct"
              />
              <other-pay
                v-else
                v-model="paycheck"
                :pay-info="item"
                @updatePayProduct="updatePayProduct"
              />
            </div>
          </div>
          <div style="height: 120px"></div>
        </div>
      </van-pull-refresh>
      <div class="bottom-confirm">
        <p class="tip">
          <img src="@/assets/images/credit/safe.png" class="icon" />
          轻享花智能加密，承诺实时保障你的信息安全
        </p>
        <div
          class="pay-btn theme-linear-gradient"
          :disabled="disabledPay"
          block
          @click="onConfirmPay"
        >
          {{ btnMsg }}
        </div>
        <div class="agreement" v-if="currentPay && currentPay.payType === $global.PAY_TYPE_BILLPAY">
          <van-checkbox v-model="checked">
            <span class="agree text-black"
              >已阅读并同意<span
                class="highlight theme-text"
                @click.stop="
                  agreementPopup
                    .reveal([
                      {
                        title: '个人信息查询及使用授权',
                        src:
                          '/agreement/personal-info-auth.htm' +
                          qs.stringify(
                            {
                              name: user?.custIdCard?.name,
                              'id-card': user?.custIdCard?.idcard,
                            },
                            {
                              addQueryPrefix: true,
                            }
                          ),
                      },
                      {
                        title: '借款合同',
                        src: '/agreement/loan-contract.htm',
                      },
                    ])
                    .then(({ isCanceled }) => !isCanceled && (checked = true))
                "
                >《授权相关协议》</span
              ></span
            >
          </van-checkbox>
        </div>
        <!-- <div :class="`pay-btn ${disabledPay?'disabled':''}`" @click="onConfirmPay">
          {{ btnMsg }}
        </div> -->
      </div>
    </div>
    <van-popup v-model:show="retainShow" :style="{ borderRadius: '7px' }">
      <div class="retain-context">
        <div class="title">确认离开收银台？</div>
        <div class="tips">
          <span>您的订单在</span
          ><van-count-down style="display: inline-block" :time="time" format="HH 时 mm 分" /><span
            >内未支付将被取消，请尽快支付</span
          >
        </div>
        <div class="btn-group">
          <div class="btn pay theme-bg" @click="retainShow = false">继续支付</div>
          <div class="btn back" @click="onBackClick">确认离开</div>
        </div>
      </div>
    </van-popup>
    <authorize-popup ref="authorizePopupRef" />
    <bind-card-explain ref="bindCardExplainRef" @toBindCard="toBindCardBill" />
    <bill-sms-valid
      ref="billSmsValidRef"
      v-model="orderId"
      @billValidSucc="billValidSucc"
    ></bill-sms-valid>
    <credit-loading ref="loadingRef" />
    <refuse-popup
      v-if="currentPay.creditInfo && currentPay.creditInfo.allowTime"
      ref="refusePopupRef"
      :allow-time="currentPay.creditInfo.allowTime"
    />
    <agreement-component ref="AgreementRef" v-model="showAgreementList" />
    <AgreementPopup ref="agreementPopup"></AgreementPopup>
  </div>
</template>

<script setup>
import { useTemplateRef } from 'vue'
import qs from 'qs'
import InstalmentPay from './components/InstalmentPay'
import BankCardPay from './components/BankCardPay'
import OtherPay from './components/OtherPay'
import PayLater from './components/PayLater'
import { payProduct, submitPay, chnlSmsSubmit } from '@/api/cashier'
import { bankCardList } from '@/api/bankcard'
import { creditQuota, saveContacts } from '@/api/customer'
import { showToast, showDialog } from 'vant'
import BindCardExplain from './components/BindCardExplain'
import BillSmsValid from './components/BillSMSValid'
import CreditLoading from '@/components/CreditLoading'
import RefusePopup from './components/RefusePopup'
import AuthorizePopup from './components/authorizePopup'
import AgreementComponent from './components/AgreementComponent'
import { useGetPhoneInfo } from '@/hooks/useGetPhoneInfo'
import AgreementPopup from '@/views/creditFacilities2/AgreementPopup.vue'
const agreementPopup = useTemplateRef('agreementPopup')
const { proxy } = getCurrentInstance()
const isIphoneX = window.isIphoneX
const router = useRouter()
const route = useRoute()
const store = useStore()
const user = computed(() => store.getters.userInfo)
const checked = ref(true) // 协议勾选
const showAgreementList = ref(false) // 协议列表
const agreementTitle = ref('')
const agreementUrl = ref('')
const agreementRef = ref(null)
const retainShow = ref(false) // 挽留
const instalmentRef = ref(null)
const bindCardExplainRef = ref(null)
const billSmsValidRef = ref(null)
const refusePopupRef = ref(null)
const orderId = ref('')
const orderSource = ref('')
const payList = ref([])
const paycheck = ref(0) //支付产品选择
const amount = ref(0) // 订单金额
const btnMsg = ref('确定') // 底部按钮
const vipBuyFlag = ref('N') // 会员商品标识
const memberPopupRef = ref(null)
const loadingRef = ref(null)
const verificationCodeRef = ref(null)
const loading = ref(false) // 下拉刷新
const data = reactive({
  currentPay: {},
})
const authorizePopupRef = ref(null)
const { currentPay } = toRefs(data)
const payType = ref('') // 支付类型
const billFlag = ref('') // 是否支持分期
const creditState = ref('') // 授信状态
const disabledPay = ref(false)
const periodsChecked = ref(0)
const subProduct = ref(0) // 新消费分期产品id
const checkBankId = ref(0)
const billBankId = ref(null)
const time = ref(0)
// 支付产品列表滚动值
const payListScrollTop = ref(0)
// 当支付产品列表滚动大于0时，禁用下拉
const pullRefreshDisabled = computed(() => {
  return payListScrollTop.value > 0
})
// 监听支付产品列表滚动
const onPayListScroll = (event) => {
  payListScrollTop.value = event.target.scrollTop
}
// 确认支付
const onConfirmPay = () => {
  if (!paycheck.value) {
    // 支付方式勾选
    showToast('请选择支付方式')
    return
  }
  payHandle()
}
// 支付处理
const payHandle = () => {
  if (currentPay.value.payType === proxy.$global.PAY_TYPE_BILLPAY) {
    if (!checked.value) {
      showToast('请勾选并同意协议')
      return
    }
    billPay()
  } else if (currentPay.value.payType === proxy.$global.PAY_TYPE_ALIPAY) {
    alipay()
  } else if (currentPay.value.payType === proxy.$global.PAY_TYPE_WECHATPAY) {
    wxpay()
  } else if (currentPay.value.payType === proxy.$global.PAY_TYPE_QUICKPAY) {
    quickPay()
  } else if (currentPay.value.payType === proxy.$global.PAY_TYPE_PAYLATER) {
    paylater()
  }
}
const creditFun = () => {
  // const name =
  //   user.value.flow === proxy.$global.USER_FLOW_INFO
  //     ? proxy.$global.USER_FLOW_INFO_NAME
  //     : proxy.$global.USER_FLOW_IDCARD_NAME
  // localStorage.setItem(proxy.$global.LOCAL_CREDIT_SCENE, proxy.$global.CREDIT_SCENE_CONSUME) // 实名授信通道
  // router.push({ name, params: { backUrl: route.href } })

  router.push({
    path: '/credit-facilities',
    query: {
      bankChannelId: currentPay.value.bankChannelId,
      cmId: currentPay.value.cmId,
      orderId: orderId.value,
      productId: currentPay.value.productId,
      // requestType: 'credit',
      // sceneCode: proxy.$global.CREDIT_SCENE_CONSUME,
    },
  })
}
// 分期支付
const billPay = async () => {
  if (user.value.flow !== proxy.$global.USER_FLOW_FINISH) {
    // 待实名
    creditFun()
    return
  }
  if (currentPay.value.creditInfo.creditStatus === 'NON') {
    // 待授信
    if (user.value.contactsFlag !== 'Y') {
      // 获取通讯录
      // authorizePopupRef.value.show = true

      const { data, isCanceled } = await authorizePopupRef.value.reveal()
      if (isCanceled) return
      if (!isCanceled && data?.phoneInfo) {
        saveContacts({ contacts: data.phoneInfo }).then(() => {
          store.dispatch('GetInfo')
        })
      }
    }
    // 直接申请授信
    submiCreditQuota()
    return
  }
  if (currentPay.value.creditInfo.creditStatus === 'APPLY') {
    showDialog({
      title: '提示',
      message: '您提交授信额度计算中，请耐心等待',
    }).then(() => {
      // on close
    })
    return
  }
  if (currentPay.value.creditInfo.creditStatus === 'FAIL') {
    showDialog({
      title: '提示',
      message: '当前支付方式未通过授信',
    })
      .then(() => {
        // on close
      })
      .catch(() => {})
    return
  }
  if (currentPay.value.existBill) {
    showDialog({
      title: '提示',
      message: '您有消费分期订单未结清，请还款后再申请!',
    })
      .then(() => {
        router.push('/my/bill')
      })
      .catch(() => {})
    return
  }
  if (currentPay.value.creditInfo.creditStatus === 'SUCC') {
    if (!checked) {
      // 协议勾选
      return
    }
    if (billFlag.value === 'N') {
      // 非分期订单
      showToast('该订单不支持分期支付！')
      return
    }
    // if(amount.value < 1000 ) { // 非分期订单
    //   showToast('订单金额小于1000不支持分期支付！')
    //   return
    // }
    // if(currentPay.value.quota.consumeUltimaAmount < amount.value) { // 消费额度
    //   showToast('消费额度不足！')
    //   return
    // }
    if (!periodsChecked.value) {
      showToast('请选择分期期数')
      return
    }

    if (
      currentPay.value.creditInfo.loanVerifyFlag === 'N' &&
      new Date().getTime() < currentPay.value.creditInfo.allowTime
    ) {
      // 用信审核不通过
      refusePopupRef.value.show = true
      return
    }
    // 用信是否需要获取通讯录
    if (user.value.contactsFlag !== 'Y') {
      // authorizePopupRef.value.show = true
      const { data, isCanceled } = await authorizePopupRef.value.reveal()
      if (isCanceled) return
      if (!isCanceled && data?.phoneInfo) {
        saveContacts({ contacts: data.phoneInfo }).then(() => {
          store.dispatch('GetInfo')
        })
      }
    }
    resultSubmit()

    return
  }
}
// 先享后付
const paylater = () => {
  if (user.value.flow !== proxy.$global.USER_FLOW_FINISH) {
    // 待实名
    creditFun()
  } else {
    if (!checked) {
      // 协议勾选
      return
    }
    billPayBank()
  }
}
const resultSubmit = () => {
  // 先判断会员再进行账单支付
  // const result = memberPopupRef.value.memberCheck()
  // if(!result) {  // 无需弹框
  //   billPayBank()
  // }
  billPayBank()
}
// 绑卡处理
const billPayBank = () => {
  // 获取银行卡
  bankCardList({
    pageNum: 1,
    pageSize: 100,
    channelId: currentPay.value.bankChannelId,
    orderId: orderId.value,
    cmId: currentPay.value.cmId,
    bankCardType: currentPay.value.cardBindType,
  }).then((res) => {
    if (res.data.length > 0) {
      billSmsValidRef.value.show = true
      billSmsValidRef.value.getCode()
      if (currentPay.value.payType === proxy.$global.PAY_TYPE_BILLPAY) {
        billBankId.value = res.data[0].id
      }
    } else {
      bindCardExplainRef.value.show = true
    }
  })
}
//
const updateSubProduct = (val) => {
  // 新消费分期
  subProduct.value = val
}
// 绑卡
const toBindCardBill = () => {
  const productId =
    currentPay.value.payType === proxy.$global.PAY_TYPE_PAYLATER
      ? currentPay.value.productId
      : currentPay.value.creditInfo.productId
  router.push({
    path: '/my/bankcard/add',
    query: {
      channelId: currentPay.value.bankChannelId,
      orderId: orderId.value,
      productId,
      cmId: currentPay.value.cmId,
      returnUrl: route.href,
    },
  })
}
// 发送
const billValidSucc = async (code) => {
  const params = {
    cashierId: currentPay.value.id,
    orderId: orderId.value,
    verifyCode: code,
  }
  if (currentPay.value.paySmsFlag === 'Y') {
    // 通道验证码提交订单
    await chnlSmsSubmit(params)
      .then((res) => {
        submitPayRoute()
      })
      .catch(() => {
        console.log('')
      })
  } else {
    if (currentPay.value.payType === proxy.$global.PAY_TYPE_QUICKPAY) {
      // 快捷，先享后付
      params.id = checkBankId.value
    } else if (currentPay.value.payType === proxy.$global.PAY_TYPE_BILLPAY) {
      if (currentPay.value.financeProductId !== 0) {
        // 新消费分期
        params.subProductId = subProduct.value
      }
      params.id = billBankId.value
      params.productId = periodsChecked.value
    } else if (currentPay.value.payType === proxy.$global.PAY_TYPE_PAYLATER) {
      params.id = checkBankId.value
      params.productId = currentPay.value.trials[0].productResponse.productId
      params.subProductId = currentPay.value.trials[0].productResponse.subProductId
    }
    await submitPay(params)
      .then((res) => {
        submitPayRoute()
      })
      .catch(() => {
        console.log('')
      })
  }
  billSmsValidRef.value.show = false // 隐藏验证码
}
// 支付提交后的路由跳转
const submitPayRoute = () => {
  if (route.query.redirect) {
    router.replace({
      path: route.query.redirect,
      query: {
        success: true,
      },
    })
    return
  }

  if (route.query.back) {
    router.back()
    return
  }

  router.replace({
    path: '/pay-result',
    query: {
      orderId: orderId.value,
      state: 'Y',
      type: currentPay.value.payType,
      orderSource: orderSource.value ? orderSource.value : proxy.$global.CASHIER_GOODS_ORDER,
    },
  })
}
// 支付宝支付
const alipay = async () => {
  const { data, isCanceled } = await proxy.appJS.appAlipayClient(orderId.value, currentPay.value.id)
  if (isCanceled) return
  payClient(data)
}
// 微信支付
const wxpay = async () => {
  const { data, isCanceled } = proxy.appJS.appWxPayClient(orderId.value, currentPay.value.id)
  if (isCanceled) return
  payClient(data)
}
// 快捷支付
const quickPay = () => {
  if (!checkBankId.value) {
    showToast('请选择支付银行卡！')
    return
  }
  if (currentPay.value.paySmsFlag === 'Y') {
    // 渠道短验
    const params = {
      payType:
        currentPay.value.payType === proxy.$global.PAY_TYPE_QUICKPAY
          ? proxy.$global.PAY_TYPE_QUICKPAY
          : proxy.$global.PAY_TYPE_BILLPAY,
      orderId: orderId.value,
    }
    if (currentPay.value.payType === proxy.$global.PAY_TYPE_QUICKPAY) {
      // 快捷
      params.id = checkBankId.value
    }
    submitPay(params).then((res) => {
      billSmsValidRef.value.show = true
    })
  } else {
    // 平台短验
    billSmsValidRef.value.show = true
    billSmsValidRef.value.getCode()
  }
}
// 当前选择快捷银行
const updateCheckBank = (id) => {
  checkBankId.value = id
}
// 返回订单详情页
const onBackClick = () => {
  if (orderSource.value) {
    router.go(-1)
  } else {
    router.push({ path: '/my/goods-order-detail', query: { orderId: orderId.value } })
  }
}
// 切换支付类型
const updatePayProduct = () => {
  const payObject = payList.value.filter((item) => item.id === paycheck.value)
  currentPay.value = payObject[0]
  if (payObject.length > 0 && payObject[0].payType === proxy.$global.PAY_TYPE_BILLPAY) {
    // 分期、走授信状态处理
    const creditInfo = payObject[0].creditInfo
    if (creditInfo.creditStatus === 'NON' || user.value.flow !== proxy.$global.USER_FLOW_FINISH) {
      // 未授信
      btnMsg.value = '前往激活额度'
      disabledPay.value = false
    } else if (creditInfo.creditStatus === 'SUCC') {
      btnMsg.value = '确认并支付￥' + amount.value
      disabledPay.value = false
    } else {
      disabledPay.value = true
    }
  } else {
    periodsChecked.value = 0
    btnMsg.value = '确认并支付￥' + amount.value
    // btnMsg.value = '确认'
    disabledPay.value = false
  }
}
// 分期期数选择
const updatePeriods = (val, update) => {
  periodsChecked.value = val
  if (!update) {
    // 默认选中分期不更新、
    updatePayProduct()
  }
}
const getPayProduct = () => {
  payProduct({ orderId: orderId.value })
    .then((res) => {
      loading.value = false
      time.value = res.data.outTime * 1000
      amount.value = res.data.payAmount
      vipBuyFlag.value = res.data.vipBuyFlag
      payList.value = res.data.cashiers
      billFlag.value = res.data.billFlag

      if (paycheck.value && !payList.value.find((v) => v.id === paycheck.value)) {
        // 刷新后选中的支付方式已消失清除选择
        paycheck.value = null
      }

      if (res.data.billFlag === 'N') {
        // 不支持分期
        for (let i = 0; i < payList.value.length; i++) {
          if (payList.value[i].payType !== proxy.$global.PAY_TYPE_BILLPAY) {
            // 排除分期第一条
            paycheck.value = paycheck.value || payList.value[i].id
            break
          }
        }
      } else {
        paycheck.value = paycheck.value || payList.value[0].id
      }

      // 当订单不支持分期，并且支付方式只有分期（不执行默认选择支付方式）
      if (
        !(
          res.data.billFlag === 'N' &&
          payList.value.length === 1 &&
          payList.value[0].payType === proxy.$global.PAY_TYPE_BILLPAY
        )
      ) {
        updatePayProduct()
      }
    })
    .catch(() => {
      loading.value = false
    })
}
// 支付返回结果
const payClient = (data) => {
  console.log('payClient', data)
  const result = typeof data === 'string' ? JSON.parse(data) : data
  router.replace({
    path: '/pay-result',
    query: {
      orderId: orderId.value,
      state: result.payResult,
      orderSource: orderSource.value ? orderSource.value : proxy.$global.CASHIER_GOODS_ORDER,
    },
  })
}
// 授信
const submiCreditQuota = (data) => {
  loadingRef.value.show = true
  const creditParam = { requestType: 'credit', sceneCode: proxy.$global.CREDIT_SCENE_CONSUME }
  if (data) {
    creditParam.useDevice = data
  }
  creditQuota({ ...creditParam })
    .then((res) => {
      setTimeout(() => {
        loadingRef.value.show = false
        // 授信后重新获取账单信息
        getPayProduct()
      }, 1500)
    })
    .catch(() => {
      loadingRef.value.show = false
    })
}
// 通讯录回调
const getPhoneInfo = (params) => {
  useGetPhoneInfo(params, () => {
    store.dispatch('GetInfo')
  })
  if (currentPay.value.creditInfo.creditStatus === 'NON') {
    // 发起授信
    submiCreditQuota()
  } else {
    // 发起用信
    resultSubmit()
  }
}
// 经纬度回调
const getLocationInfo = (data) => {
  console.log('getLocationInfo', data)
}
// 发起授权
const authorizeGet = async (type) => {
  if (type === 'getPhoneInfo') {
    // 通讯录
    const data = await proxy.appJS.appGetPhoneInfo()
    getPhoneInfo(data)
  } else {
    // 经纬度、
    const data = await proxy.appJS.appGetLocationInfo()
    getLocationInfo(data)
  }
}
onMounted(() => {
  orderId.value = route.query.orderId
  orderSource.value = route.query.orderSource
  getPayProduct()
  // 注册支付结果桥
  // window.payClient = payClient
  // window.getPhoneInfo = getPhoneInfo
  // window.getLocationInfo = getLocationInfo
})
// 下拉刷新
const onRefresh = () => {
  getPayProduct()
}
const agreementShow = (type) => {
  if (type === 'persol') {
    agreementUrl.value = '/agreement/wan-heng-vip.htm'
    agreementTitle.value = '恒青权益平台权益服务协议'
  } else {
    agreementUrl.value = '/agreement/member-service.htm'
    agreementTitle.value = '会员服务协议'
  }
  agreementRef.value.show = true
}
</script>

<style lang="scss" scoped>
.cashier-page {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  position: absolute;
  &-context {
    flex-grow: 1;
    overflow: hidden;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    background: #f4f4f4;
    .amount-time {
      background: #ffffff;
      border-radius: 10px;
      margin: 10px 10px 0px 10px;
      padding: 20px 0;
      text-align: center;
      .amount {
        font-size: 30px;
        font-weight: bold;
        color: #ff4019;
        line-height: 40px;
        &::first-letter {
          font-size: 14px;
        }
      }
      .time {
        font-weight: 400;
        font-size: 18px;
        color: #666666;
        line-height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .van-count-down {
        color: #666666;
        font-size: 18px;
      }
    }
    .bottom-confirm {
      width: calc(100% - 32px);
      // padding-bottom: constant(safe-area-inset-bottom); /*兼容 IOS<11.2*/
      // padding-bottom: env(safe-area-inset-bottom); /*兼容 IOS>11.2*/
      background: #ffffff;
      padding: 12px 16px calc(12px + var(--safe-area-inset-bottom));

      .tip {
        text-align: center;
        color: #bcbcbc;
        font-size: 13px;
        .icon {
          display: inline-block;
          width: 12px;
          height: 16px;
        }
      }
      .agreement {
        display: flex;
        font-size: 12px;
        align-items: center;
        line-height: 22px;
        margin-top: 8px;
        .van-checkbox {
          --van-checkbox-size: 16px;
        }
        .agree {
          margin-left: 3px;
        }
        .highlight {
          display: inline-block;
        }
      }
      .pay-btn {
        text-align: center;
        height: 40px;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 400;
        color: #ffffff;
        line-height: 40px;
        margin-top: 8px;
        // margin-bottom: 10px;
      }
      .pay-btn.disabled {
        background: #dddddd;
      }
    }
    .bottom-confirm.iphonex {
      // padding-bottom: 34px;
      padding-bottom: var(--safe-area-inset-bottom);
    }
    .pay-list-wrapper {
      flex: 1;
      height: 100%;
      overflow: hidden;
      overflow-y: auto;

      .pay-list {
        background-color: #fff;
        border-radius: 8px;
        padding-bottom: var(--safe-area-inset-bottom);
        margin: 10px;
        .pay-title {
          font-weight: bold;
          font-size: 14px;
          color: #222222;
          line-height: 22px;
          padding: 10px 0 12px 8px;
        }
        .pay-item {
          .title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            img {
              width: 16px;
              height: 16px;
            }
          }
        }
        .pay-item + .pay-item {
          margin-top: 10px;
        }
      }
    }
  }
  .agrenmnet-title {
    padding: 16px 0;
    text-align: center;
    font-size: 16px;
    font-family: PingFang-SC-Bold, PingFang-SC;
    font-weight: bold;
    color: #363636;
    line-height: 22px;
  }
  .agrenment-item {
    padding: 10px 0;
    text-align: center;
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #222222;
    line-height: 20px;
  }
}
.retain-context {
  background: #ffffff;
  padding: 20px;
  width: 195px;
  .title {
    font-size: 16px;
    font-weight: bold;
    color: #222222;
    line-height: 22px;
    text-align: center;
  }
  .tips {
    font-size: 13px;
    font-weight: 400;
    color: #666666;
    line-height: 18px;
    margin-top: 10px;
  }
  .btn-group {
    display: flex;
    margin-top: 26px;
    .btn {
      width: 90px;
      height: 32px;
      border-radius: 7px;
      font-size: 14px;
      font-weight: 400;
      line-height: 32px;
      text-align: center;
    }
    .btn.pay {
      color: #ffffff;
    }
    .btn.back {
      background: #eeeeee;
      color: #333333;
      margin-left: 13px;
    }
  }
}
</style>
