<template>
  <van-popup v-model:show="show" position="bottom" :close-on-click-overlay="false" round closeable>
    <div class="bill-sms-valid">
      <div class="title">请输入短信验证码</div>
      <div class="tips">已发送短信验证码至 {{ phoneFormat(user.phone) }}</div>
      <div class="password-input">
        <van-password-input
          :value="value"
          :mask="false"
          :gutter="15"
          :error-info="errorInfo"
          :focused="showKeyboard"
          @focus="showKeyboard = true"
        />
      </div>
      <!-- <div :class="`sms-get ${times === 0 ? '' : 'disabled'}`" @click="getCode">{{codeText}}</div> -->
      <div class="sms-get"></div>
      <div class="number-keyboard" :class="{ 'iphonex-bottom': isIphoneX }">
        <div class="van-number-keyboard__body">
          <div class="van-number-keyboard__keys">
            <div
              class="van-key__wrapper"
              v-for="item in keyNumber"
              :key="item"
              @click="onKeyNumber(item)"
            >
              <div role="button" tabindex="0" class="van-key">{{ item }}</div>
            </div>
            <div class="van-key__wrapper"></div>
            <div class="van-key__wrapper">
              <div role="button" tabindex="0" class="van-key" @click="onKeyNumber(0)">0</div>
            </div>
            <div class="van-key__wrapper" @click="delKeyNumber()">
              <div role="button" tabindex="0" class="van-key">
                <img class="key-delete" src="@/assets/icons/key-delete.png" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </van-popup>
</template>

<script setup>
import { phoneFormat } from '@/utils/common'
import { smsCode } from '@/api/cashier'
import { showToast } from 'vant'
const props = defineProps({
  modelValue: {
    type: String,
    default: '',
  },
})
const store = useStore()
const user = computed(() => store.getters.userInfo)
const show = ref(false)
const emit = defineEmits(['billValidSucc'])
const isIphoneX = window.isIphoneX
const keyNumber = [1, 2, 3, 4, 5, 6, 7, 8, 9]
const value = ref('')
const errorInfo = ref('')
const codeText = ref('重新获取')
const showKeyboard = ref(false)
const times = ref(0)
watch(value, (newVal) => {
  if (newVal.length === 6) {
    emit('billValidSucc', value.value)
    value.value = ''
  }
})
const onKeyNumber = (val) => {
  value.value += val
}
const delKeyNumber = () => {
  value.value = value.value.substr(0, value.value.length - 1)
}
// 获取短信验证码
const getCode = () => {
  if (times.value === 0) {
    times.value = 60
    codeText.value = '重新获取（' + times.value + 's）'
    const timer = setInterval(function () {
      times.value--
      codeText.value = '重新获取（' + times.value + 's）'
      if (times.value === 0) {
        clearInterval(timer)
        codeText.value = '重新获取'
      }
    }, 1000)
    smsCode({ orderId: props.modelValue }).then((res) => {
      showToast('发送成功，请注意查收')
      // if (res.code) {
      //   value.value = res.code
      // }
    })
  }
}
defineExpose({ show, getCode, value })
</script>

<style lang="scss" scoped>
.bill-sms-valid {
  .title {
    font-size: 20px;
    font-family: PingFang-SC-Bold, PingFang-SC;
    font-weight: bold;
    color: #363636;
    line-height: 28px;
    margin-top: 20px;
    margin-left: 25px;
  }
  .tips {
    margin-top: 4px;
    font-size: 12px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #868686;
    line-height: 17px;
    margin-left: 25px;
    margin-bottom: 28px;
  }
  .sms-get {
    margin-top: 22px;
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #222222;
    line-height: 20px;
    margin-left: 25px;
    margin-bottom: 46px;
  }
  .sms-get.disabled {
    color: #a8a8a8;
  }
  .password-input {
    margin-top: 28px;
    padding: 0 10px;
  }
}
:deep(.van-password-input__item) {
  border: 1px solid #ececec;
}
:deep(.van-password-input__security) {
  height: 40px;
}
:deep(.van-password-input__item--focus) {
  border-bottom: 1px solid #363636;
}
.number-keyboard {
  background: #f2f3f5;
  padding-bottom: 22px;
}
.number-keyboard.iphonex-bottom {
  // padding-bottom: 34px;
  padding-bottom: calc(22px + var(--safe-area-inset-bottom));
}
.key-delete {
  width: 32px;
  height: 22px;
}
</style>
