<template>
  <div class="my-address-form-page">
    <navigation-bar
      @onLeftClick="onBackClick"
      :pageName="title"
      :navBarStyle="{ fontWeight: 600, backgroundColor: '#f9f9f9' }"
    />
    <div class="my-address-form-page-context" :class="{ 'iphonex-bottom': isIphoneX }">
      <van-form ref="formRef" @submit="onSubmit">
        <van-cell-group inset>
          <van-field
            v-model="addressInfo.contactName"
            name="contactName"
            label="收货人"
            placeholder="请填写收货人"
            :rules="[{ required: true, validator: validatorName, message: '请填写收货人' }]"
          />
          <van-field
            v-model="addressInfo.contactTel"
            name="contactTel"
            label="手机号码"
            type="tel"
            placeholder="请填写手机号码"
            :rules="[{ required: true, validator: validatorPhone, message: '请填写手机号码' }]"
          />
          <van-field
            v-model="addressInfo.areaData"
            is-link
            readonly
            label="所在地址"
            placeholder="点击选择省市区"
            :rules="[{ validator: validArea, message: '请选择省市区' }]"
            @click="showArea = true"
          />
          <van-field
            label="详情地址"
            v-model.trim="addressInfo.address"
            placeholder="请补充详细地址 如：XX路/街道XX号XX小区XX栋XX"
            type="textarea"
            :rules="[{ required: true, message: '请填写详细地址' }]"
          />
        </van-cell-group>
        <div class="swith-warpper">
          <div class="label">
            <div class="title">设为默认地址</div>
            <div class="tips">提醒：每次下单会默认推荐使用该地址</div>
          </div>
          <van-switch v-model="checked" active-color="#4671EB" />
        </div>
        <div class="footer" :class="{ 'iphonex-bottom': isIphoneX }">
          <div class="btn theme-linear-gradient" @click="validateForm()">保存</div>
        </div>
      </van-form>
    </div>
    <van-popup v-model:show="showArea" position="bottom">
      <van-area
        v-model="addressInfo.districtCode"
        :area-list="areaList"
        @confirm="onAreaConfirm"
        @cancel="showArea = false"
      />
    </van-popup>
  </div>
</template>

<script setup>
import NavigationBar from '@/components/NavigationBar'
import { areaList } from '@vant/area-data'
import { validPhone } from '@/utils/common'
import { addAddress, updateAddress } from '@/api/address'
const { proxy } = getCurrentInstance()
const router = useRouter()
const isIphoneX = window.isIphoneX
const checked = ref(false)
const title = ref('')
const formRef = ref(null)
const onBackClick = () => {
  router.go(-1)
}
const data = reactive({
  addressInfo: {},
})
const { addressInfo } = toRefs(data)
const validArea = (val) => (val ? true : false)
const validatorName = (val) => {
  const regStr = /^\s*$/
  if (regStr.test(val)) {
    return '收货人不能为空'
  }
}
const validatorPhone = (val) => {
  if (!validPhone(val)) {
    return '请填写正确的手机号码'
  }
}
const showArea = ref(false)
const onAreaConfirm = (areaValues) => {
  addressInfo.value.areaData = areaValues.selectedOptions
    .filter((item) => !!item)
    .map((item) => item.text)
    .join('/')
  addressInfo.value.province = areaValues.selectedOptions[0].text
  addressInfo.value.city = areaValues.selectedOptions[1].text
  addressInfo.value.district = areaValues.selectedOptions[2].text
  addressInfo.value.districtCode = areaValues.selectedOptions[2].value
  showArea.value = false
}
// 校验表单
const validateForm = async () => {
  try {
    await formRef.value.validate()
    onSubmit()
  } catch {
    console.log('校验不通过')
  }
}
// 提交
const onSubmit = () => {
  addressInfo.value.isDefault = checked.value ? 'Y' : 'N'
  if (addressInfo.value.id) {
    updateAddress({ address: addressInfo.value }).then((res) => {
      proxy.onToastSucc(() => {
        router.go(-1)
      }, '编辑成功！')
    })
  } else {
    addAddress({ address: addressInfo.value }).then((res) => {
      proxy.onToastSucc(() => {
        router.go(-1)
      }, '新增成功！')
    })
  }
}
onMounted(() => {
  const info = sessionStorage.getItem('address-form')
  if (info) {
    addressInfo.value = JSON.parse(info)
    checked.value = addressInfo.value.isDefault === 'Y' ? true : false
    addressInfo.value.areaData =
      addressInfo.value.province + '/' + addressInfo.value.city + '/' + addressInfo.value.district
    title.value = '编辑地址'
  } else {
    title.value = '新增地址'
  }
})
</script>

<style lang="scss" scoped>
.my-address-form-page {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  position: absolute;
  background: #f9f9f9;
  .nav-bar {
    border-bottom: none;
  }
  &-context {
    flex-grow: 1;
    overflow: hidden;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    position: relative;
    .swith-warpper {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: #ffffff;
      border-radius: 8px;
      margin: 10px;
      padding: 14px 16px;
      .title {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #333;
        line-height: 22px;
      }
      .tips {
        font-size: 12px;
        transform: scale(0.85);
        transform-origin: left top;
        color: #666666;
        line-height: 14px;
      }
    }
  }
}
:deep(.van-field__label) {
  color: #333;
}
.van-cell-group--inset {
  margin: 10px;
}
.footer {
  background: #ffffff;
  padding: 7px 16px;
  position: fixed;
  bottom: 0;
  width: calc(100% - 32px);
  .btn {
    border-radius: 7px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    font-size: 14px;
    color: #ffffff;
  }
}
.footer.iphonex-bottom {
  // padding-bottom: 51px !important;
  padding-bottom: calc(7px + var(--safe-area-inset-bottom));
}
</style>
