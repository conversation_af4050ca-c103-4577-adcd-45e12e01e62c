<template>
  <div class="bankcard-add-page">
    <navigation-bar pageName="绑定银行卡" @onLeftClick="onBackClick"></navigation-bar>
    <div class="bankcard-add-page-context" :class="{ 'iphonex-bottom': isIphoneX }">
      <div class="tips">
        <img class="bank-icon" src="@/assets/images/my/bank-icon.png" />
        <div class="text">
          <span class="text-bold">请添加银行卡</span>
          <span class="theme-text">建议使用常用银行卡</span>
        </div>
      </div>
      <van-form v-if="form.custIdCard" ref="formSubmit" @submit="onSubmit">
        <van-field
          v-if="user.flow !== $global.USER_FLOW_IDCARD"
          v-model="form.custIdCard.shieldName"
          label="持卡人"
          readonly
        />
        <van-field
          v-else
          v-model="form.custIdCard.name"
          label="持卡人"
          placeholder="请填写本人姓名"
          :rules="[{ required: true, message: '请填写本人姓名' }]"
        />
        <van-field
          v-if="user.flow === $global.USER_FLOW_IDCARD"
          v-model="form.custIdCard.idcard"
          label="持卡人身份证"
          placeholder="请填写持卡人身份证"
          :rules="[{ required: true, message: '请填写持卡人身份证' }]"
        />
        <van-field
          v-model="form.bankCard.cardNo"
          label="银行卡号"
          type="number"
          pattern="[0-9]*"
          placeholder="请填写银行卡号"
          :formatter="formatter"
          maxlength="25"
          @blur="onBankBlur"
          :rules="[{ required: true, message: '请填写银行卡卡号' }]"
        >
          <template v-if="form.bankCard.cardNo" #right-icon>
            <van-icon name="close" @click="clearCardNo" />
          </template>
        </van-field>
        <van-field v-model="form.bankName" label="银行名称" placeholder="请填写银行名称" readonly>
          <template #right-icon>
            <van-icon name="warning-o" @click="supportingBanksRef.show = true" />
          </template>
        </van-field>
        <van-field
          v-model="form.bankCard.phone"
          label="手机号码"
          placeholder="请填写银行预留手机号码"
          type="tel"
          maxlength="11"
          :rules="[{ required: true, message: '请填写银行预留手机号码' }]"
        >
          <template #right-icon>
            <van-icon name="warning-o" @click="phoneShow = true" />
          </template>
        </van-field>
      </van-form>
      <div :class="`bottom-confirm ${isIphoneX ? 'iphonex' : ''}`">
        <div class="agreement">
          <van-checkbox v-model="checked" icon-size="16px" :checked-color="$global.THMEM">
            <span class="agree"
              >已阅读并同意<span class="highlight theme-text" @click.stop="agreementShow = true"
                >《账户委托扣款授权书》</span
              >、<span
                class="highlight theme-text"
                @click.stop="digitalCertificateProtocolPopupRef.reveal()"
                >《数字证书协议》</span
              ></span
            >
          </van-checkbox>
        </div>
        <div class="submit-btn theme-linear-gradient" @click="handleSubmit">确定</div>
      </div>
    </div>
    <van-popup
      v-model:show="agreementShow"
      safe-area-inset-bottom
      :style="{ height: '100%', width: '100%' }"
      class="full-screen"
    >
      <navigation-bar :isShowBack="false" pageName="账户委托扣款授权书">
        <template #nav-left>
          <van-icon name="cross" size="22" class="text-gray" @click="agreementShow = false" />
        </template>
      </navigation-bar>
      <iframe
        class="external-links"
        src="/agreement/account_auth.htm"
        scrolling="auto"
        frameborder="0"
        id="iframe"
      ></iframe>
    </van-popup>
    <van-popup v-model:show="smsShow" round :close-on-click-overlay="false">
      <div class="sms-popup">
        <div class="phone-text">确认手机号</div>
        <div class="tips">已发送验证码短信到</div>
        <div class="phone-hide">{{ phoneFormat(form.bankCard.phone) }}</div>
        <van-field v-model="code" placeholder="请输入验证码" type="number" @input="resetInputVal">
          <template #button>
            <van-button
              v-if="times === 0"
              class="sms-btn theme-text"
              size="small"
              round
              type="text"
              @click="getCode"
              >发送验证码</van-button
            >
            <van-button v-else class="sms-btn text-gray" size="small" round type="text"
              >{{ times }}s</van-button
            >
          </template>
        </van-field>
        <div class="btn-wrapper">
          <div class="cancel-btn btn" @click="smsClose">取消</div>
          <div class="confirm-btn btn theme-linear-gradient" @click="onBindCard">确认</div>
        </div>
      </div>
    </van-popup>
    <van-popup v-model:show="phoneShow" round closeable>
      <div class="phone-popup">
        <div class="title">预留手机号</div>
        <div class="tips">
          该手机号码是您办理该银行卡时在银行填写的号码，如有疑问请联系该银行客服
        </div>
        <div class="btn theme-linear-gradient" @click="phoneShow = false">确认</div>
      </div>
    </van-popup>
    <!-- <van-popup v-model:show="bankShow" :style="{'width': '90%'}" closeable>
      <img class="support-bank-img" src="@/assets/images/my/support-bank.png">
    </van-popup> -->
    <supporting-banks ref="supportingBanksRef"></supporting-banks>
    <digital-certificate-protocol-popup
      ref="digitalCertificateProtocolPopup"
    ></digital-certificate-protocol-popup>
  </div>
</template>

<script setup>
import NavigationBar from '@/components/NavigationBar'
import {
  saveBankCard,
  saveBindCard,
  getBankName,
  getBankSmsCode,
  getBankListGroup,
} from '@/api/bankcard'
import { showFirstName, phoneFormat, plusXing } from '@/utils/common'
import SupportingBanks from '@/components/SupportingBanks'
import { showToast } from 'vant'
import { useTemplateRef } from 'vue'
import DigitalCertificateProtocolPopup from '@/views/member/components/memberBank/DigitalCertificateProtocolPopup.vue'
const digitalCertificateProtocolPopupRef = useTemplateRef('digitalCertificateProtocolPopup')

const { proxy } = getCurrentInstance()
const isIphoneX = window.isIphoneX
const store = useStore()
const router = useRouter()
const route = useRoute()
const user = computed(() => store.getters.userInfo)
const checked = ref(true)
const agreementShow = ref(false)
const formSubmit = ref(null)
const code = ref('')
const smsShow = ref(false)
const bankShow = ref(false)
const phoneShow = ref(false)
const newCardId = ref(0)
const bankName = ref('')
const supportingBanksRef = ref(null)
const times = ref(0)
// 格式化银行卡
const formatter = (value) => {
  return value.replace(/\s/, '').replace(/([0-9]{4})(?=[0-9])/g, '$1 ')
}
let timer = null
const data = reactive({
  form: {},
})
const { form } = toRefs(data)
const reset = () => {
  form.value = {
    bankCard: {
      channelId: '',
      phone: '',
      cardNo: '',
    },
    custIdCard: {
      name: '',
      idcard: '',
    },
    bankName: '',
  }
}
const onBackClick = () => {
  router.go(-1)
}
const handleSubmit = () => {
  formSubmit.value.submit()
}
// 提交绑定
const onSubmit = () => {
  if (!checked.value) {
    showToast({
      message: '请阅读协议并同意协议',
    })
    return
  }
  form.value.bankCard.cardNo = form.value.bankCard.cardNo.replace(/\s/g, '')
  form.value.returnUrl = route.query.returnUrl
  saveBankCard(form.value).then((res) => {
    if (res.data.status === 'VALID') {
      proxy.onToastSucc(() => {
        router.go(-1)
      }, '绑卡成功')
    } else {
      if (res.data.signMode === 'API') {
        // 内部验证码
        countDown()
        code.value = ''
        smsShow.value = true
        newCardId.value = res.data.id
      } else if (res.data.signMode === 'PAGE') {
        // 需要去通道绑卡
        proxy.appJS.appOtherWebView(res.data.submitUrl)
      }
    }
  })
}
const resetInputVal = () => {
  const newCode = '' + code.value
  if (newCode > 6) {
    code.value = newCode.substring(0, 6)
  }
}
// 重新获取验证码
const getCode = () => {
  if (times.value === 0) {
    countDown()
    getBankSmsCode({ id: newCardId.value })
  }
}
const countDown = () => {
  times.value = 60
  timer = setInterval(function () {
    times.value--
    if (times.value === 0) {
      clearInterval(timer)
    }
  }, 1000)
}
const smsClose = () => {
  smsShow.value = false
  clearInterval(timer)
}
// 验证码确认
const onBindCard = () => {
  if (code.value) {
    saveBindCard({ verifyCode: code.value, id: newCardId.value }).then((res) => {
      proxy.onToastSucc(() => {
        if (route.query.replace) {
          router.replace(route.query.replace)
        } else {
          router.go(-1)
        }
      }, '绑定成功')
    })
  } else {
    showToast('请填写验证码！')
  }
}
const onBankBlur = () => {
  const _cardNo = form.value.bankCard.cardNo
  if (_cardNo && _cardNo.length > 15) {
    getBankName({ bankCard: { cardNo: _cardNo.replace(/\s/g, '') } }).then((res) => {
      form.value.bankName = res.data.bankName
    })
  } else {
    form.value.bankName = ''
  }
}
const clearCardNo = () => {
  form.value.bankCard.cardNo = ''
  form.value.bankName = ''
}
onMounted(() => {
  reset()
  form.value.productId = route.query.productId
  form.value.bankCard.channelId = route.query.channelId
  form.value.orderId = route.query.orderId
  form.value.cmId = route.query.cmId
  if (user.value.flow !== proxy.$global.USER_FLOW_IDCARD) {
    form.value.custIdCard.name = user.value.custIdCard.name
    form.value.custIdCard.shieldName = showFirstName(user.value.custIdCard.name)
    form.value.custIdCard.idcard = user.value.custIdCard.idcard
    form.value.custIdCard.shieldId = plusXing(user.value.custIdCard.idcard, 14, 0)
  }
  getBankListGroup().then((res) => {
    if (res.data?.length > 0) {
      // 默认银行卡
      form.value.bankCard.cardNo = res.data[0].cardNo
      form.value.bankName = res.data[0].bankName
      form.value.bankCard.phone = res.data[0].wholePhone
    }
  })
})
</script>

<style lang="scss" scoped>
.bankcard-add-page {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  position: absolute;
  .nav-bar {
    border: none;
  }
  &-context {
    flex-grow: 1;
    overflow: hidden;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    background: #f9f9f9;
    position: relative;
    .tips {
      font-size: 14px;
      height: 50px;
      display: flex;
      align-items: center;
      background: #ffffff;
      border-radius: 8px;
      margin: 15px;
      padding: 0 15px;
      .bank-icon {
        width: 33px;
        height: 33px;
        display: block;
        margin-right: 10px;
        filter: hue-rotate(232deg);
      }
    }
    .van-form {
      margin: 0 15px;
      border-radius: 8px;
      overflow: hidden;
    }
  }
}
.van-cell {
  padding: 12px 15px !important;
}
.bottom-confirm {
  width: calc(100% - 32px);
  position: fixed;
  bottom: 0;
  background: #ffffff;
  padding: 0 16px;
  .agreement {
    display: flex;
    font-size: 12px;
    align-items: center;
    line-height: 22px;
    margin-top: 7px;
    .agree {
      margin-left: 3px;
    }
    .highlight {
      display: inline-block;
    }
    .van-checkbox {
      :deep(.van-checkbox__label) {
        white-space: nowrap;
        // flex: 1;
        overflow-x: scroll;
        overflow-y: hidden;
      }
    }
  }
  .submit-btn {
    text-align: center;
    height: 40px;
    border-radius: 7px;
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #ffffff;
    line-height: 40px;
    margin-top: 5px;
    margin-bottom: 10px;
  }
}
.bottom-confirm.iphonex {
  // padding-bottom: 34px;
  padding-bottom: var(--safe-area-inset-bottom);
}
.phone-popup {
  width: 300px;
  .title {
    font-size: 16px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #363636;
    line-height: 22px;
    text-align: center;
    margin-top: 22px;
  }
  .tips {
    font-size: 13px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #a8a8a8;
    line-height: 18px;
    margin: 12px 20px 0;
  }
  .btn {
    width: 252px;
    height: 40px;
    line-height: 40px;
    border-radius: 21px;
    font-size: 14px;
    font-weight: 500;
    color: #ffffff;
    margin: 0 auto;
    margin-top: 38px;
    margin-bottom: 38px;
    text-align: center;
  }
}
.tips-bank {
  background: #ffffff;
  padding-left: 120px;
  font-size: 13px;
  padding-top: 5px;
}
.support-bank-img {
  width: 100%;
  height: 100%;
}
</style>
