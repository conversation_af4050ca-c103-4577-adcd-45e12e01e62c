<template>
  <div class="bill-repayment-page">
    <navigation-bar :pageName="productType === 'PAYLATER' ? '查看订单' : '分期详情'" @onLeftClick="onBackClick"></navigation-bar>
    <div class="bill-repayment-page-context" :class="{ 'iphonex-bottom': isIphoneX }">
      <div class="header" v-if="productType === 'PAYLATER'">
        <div class="label">
          {{ productType === 'PAYLATER' ? '会员体验到期后会自动支付' : '还款日会自动扣款' }}
          <img v-if="productType !== 'PAYLATER'" src="@/assets/icons/my/question.png" @click="showTips">
        </div>
        <div class="tips">{{ productType === 'PAYLATER' ? '您也可手动支付' : '您也可手动还款' }}</div>
      </div>
      <div class="header notPAYLATER" v-else>
        <div class="title" >分期剩余待还</div>
        <div class="price">
          <span>￥</span>{{billData.billVO?.repayAmount}}
          <!-- {{ billData.billVO.repayAmount }} -->
          <!-- {{ productType === 'PAYLATER' ? '会员体验到期后会自动支付' : '还款日会自动扣款' }} -->
        </div>
        <div class="bankLabel" @click="showTips"><span>还款日优先从{{billData.billVO?.bankName}}（{{billData.billVO?.bankCardNo?.slice(-4)}}）自动扣款</span><img src="@/assets/images/my/right-icon.png" alt=""></div>
        <div class="tips">还款日会自动扣款，您也可选择手动还款哦</div>
        <div class="bankLabel order" @click="openOrder"><span>查看订单</span><img src="@/assets/images/my/right-icon.png" alt=""></div>
      </div>
      <template v-if="billData.billVO">
        <div class="repayment-date">
          <!-- <span v-if="productType !== 'PAYLATER'">最近还款日{{ billData.billVO.repayDay }}</span> -->
          <van-checkbox
            v-if="productType !== 'PAYLATER'"
            v-model="allChecked"
            :checked-color="$global.THMEM"
            @click="clickAll"
          >
            全选
            </van-checkbox>
        </div>
        <div class="period-content">
          <!-- <div class="period-title">
            {{ billData.billVO.principal }}元 <span v-if="productType !== 'PAYLATER'">/ {{ billData.billVO.term }}期</span>
          </div> -->
          <div class="period-list">
            <van-checkbox-group v-model="indexs">
              <div
                v-for="(item, index) in billData.billPlanList"
                :key="item.planId"
                :class="`period-item ${billData.billPlanList.length > (index+1) ? 'solid-bottom' : ''} ${'SETTLE'=== item.status ? '' : 'nosettle'}`"
              >
                <van-checkbox
                  :disabled="item.status === 'SETTLE' ? true : false"
                  :name="index"
                  :checked-color="$global.THMEM"
                  @click="click = clickPeriod(event, index)"
                >
                  <div class="period-number">
                    <div v-if="productType !== 'PAYLATER'" class="period-current">
                      <span class="number">{{ item.term }}</span>/{{ item.totalTerm }}期
                      <span class="status">
                        {{item.status === SETTLE?'已守约':item.status === 'WAIT'?'待支付':item.status === 'WAIT'?'逾期':''}}
                      </span>
                  </div>
                    <div class="period-date">{{ formatDate(item.repayDay) }}</div>
                  </div>
                </van-checkbox>
                <div class="period-amount text-right">
                  <div class="repay-amount">￥{{ item.status === SETTLE ? item.actualPrincipalInterest: item.repayAmount }}(含利息)</div>
                  <div class="period-status">
                    <span v-if="item.status === 'OVERDUE'">逾期</span>
                    <img class="settle-img" v-if="item.status === SETTLE" src="@/assets/images/my/unpaidbill-yzf.png" alt="">
                  </div>
                </div>
              </div>
            </van-checkbox-group>
          </div>
        </div>
        <div style="padding-top:100px"></div>
      </template>
      <div v-if="billData?.billVO?.status!=='SETTLE'" class="solid-top bg-white repayment-fixed" :class="{ 'iphonex-bottom': isIphoneX }">
        <div :class="`repayment-btn theme-linear-gradient ${amountCpu>0 ? '' : 'disabled'}`"  @click="toRepay">
          {{ productType === 'PAYLATER' ? '立即支付' : '立即还款' }}{{ amountCpu>0 ? `￥${amountCpu}` : '' }}
        </div>
      </div>
    </div>
    <popup-tips
      ref="popupTipsRef"
      title="温馨提示"
      content="还款日上午8点开始自动扣款，请确保绑定的银行卡余额充足，或还款日前主动还款"
      btnText="我知道了"
    />

    <!-- 订单详情弹窗 -->
    <order-detail-popup
      ref="orderDetailPopupRef"
      :order-detail-info="orderInfo"
    />
  </div>
</template>

<script setup>
  import orderDetailPopup from './components/orderDetailPopup.vue'
  import NavigationBar from '@/components/NavigationBar'
  import PopupTips from '@/components/PopupTips'
  import formatNumber from 'format-number'
  import { billDetail } from '@/api/bill'
  import { getGoodsOrder } from '@/api/goods-order'
  import { showToast } from 'vant'
  import Decimal from 'decimal.js'
  const { proxy } = getCurrentInstance()
  const isIphoneX = window.isIphoneX
  const router = useRouter()
  const route = useRoute()
  const store = useStore()
  const billId = route.query.billId
  const indexs = ref([])
  const allChecked = ref(false)
  const popupTipsRef = ref(null)
  const SETTLE = 'SETTLE'
  const productType = ref('')
  const data = reactive({
    billData: {},
    orderInfo: {}
  })
  const { billData, orderInfo}  = toRefs(data)
  onMounted(() => {
    billDetail({ billId }).then(res => {
      billData.value = res.data
      productType.value = res.data.billVO.productType
    })
  })

  const getInfo = (orderId) => {
    getGoodsOrder({ orderId }).then((res) => {
      orderInfo.value = {
        ...res.data[0],
        term: billData.value.billVO.term,
        principal: billData.value.billVO.principal,
        actualInterest: billData.value.billVO.actualInterest,
      }
    })
  }

  const orderDetailPopupRef = ref(null)
  // const orderDetailInfo = reactive({
  //   orderDetail: {}
  // })
  const openOrder = () => {
    getInfo(billData.value.msOrderId)
    orderDetailPopupRef.value.show = true
    // orderDetailInfo.orderDetail = billData.value.billVO
  }

  const formatDate = (date) => {
    if (date) {
      return date.slice(0, 4) + '年' + date.slice(5, 7) + '月' + date.slice(8, 10) + '日'
    } else {
      return ''
    }
  }

  const toRepay = () => {
    if(amountCpu.value>0){
      if (indexs.value.length > 0) {
        // if(billData.value.payableFlag=='N'){
        //   showToast('商品未签收，不支持还款')
        //   return
        // }
        let amount = 0
        let planIds = []
        for(let i =0; i < indexs.value.length; i++) {
          planIds.push(billData.value.billPlanList[indexs.value[i]].planId)

          const  repayAmount = billData.value.billPlanList[indexs.value[i]].repayAmount
          amount = new Decimal(amount).plus(repayAmount).toNumber()
          // amount += billData.value.billPlanList[indexs.value[i]].repayAmount
        }
        localStorage.setItem('billPay', JSON.stringify({ planIds,
        repayChnlId: billData.value.billVO.repayChnlId,
        productId: billData.value.billVO.productId, amount, billId, cmId: billData.value.billVO.cmId }))
        router.push({ path: '/my/bill/pay', query: { productType: productType.value } })
      } else {
        showToast(productType.value === 'PAYLATER' ? '请选择支付日期' : '请选择还款期数')
      }
    }
  }
  const clickAll = () => {
    if (!allChecked.value) {
      indexs.value = []
    } else {
      let newIndexs = []
      for (let i=0; i < billData.value.billPlanList.length; i++) {
        if(billData.value.billPlanList[i].status !== SETTLE)
        newIndexs.push(i)
      }
      indexs.value = newIndexs
    }
    // allChecked.value = !allChecked.value
  }
  // 勾选中的总金额
  const amountCpu = computed(() => {
    let amount = 0
    for(let i =0; i < indexs.value.length; i++) {
      const  repayAmount = billData.value.billPlanList[indexs.value[i]].repayAmount
      amount = new Decimal(amount).plus(repayAmount).toNumber()
    }
    return amount
  })


  const clickPeriod = (event, index) => { // 动态修改选中
    // console.log("🚀 ~ clickPeriod ~ index:", index)

    if (billData.value.billPlanList[index].status !== SETTLE) {
      let newIndexs = []
      if (indexs.value.includes(index)) {
        for (let i=0; i <= index; i++) {
          if(billData.value.billPlanList[i].status !== SETTLE)
          newIndexs.push(i)
        }
      }
      indexs.value = newIndexs
      if(newIndexs.length== billData.value.billPlanList.length){
        allChecked.value = true
      }else{
        allChecked.value = false
      }
    }
  }
  const showTips = () => {
    popupTipsRef.value.show = true
  }
  const onBackClick = () => {
    router.replace({ path: '/my/consume-bill', query: {
      status: route.query.status||'',
    }})

    // router.push({ path: '/my/consume-bill', query: {
    //   status: route.query.status||'',
    // } })
  }
</script>

<style lang="scss" scoped>
  .bill-repayment-page{
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    position: absolute;
    &-context{
      flex-grow: 1;
      overflow: hidden;
      overflow-y: auto;
      display: flex;
      flex-direction: column;
      background: #F5F5F5;
      .header{
        background: #ffffff;
        padding: 30px;
        margin: 18px 10px;
        border-radius: 10px;
        &.notPAYLATER{
          padding: 10px 0 12px 0;
          .title{
            color: #999;
            text-align: center;
            font-size: 15px;
            font-style: normal;
            font-weight: 400;
            line-height: 22.5px;
          }
          .price{
            color: #000;
            font-size: 31px;
            font-style: normal;
            font-weight: 700;
            line-height: 46px;
            text-align: center;
            span{
              font-size: 23px;

            }
          }
          .tips{
            width: 262.5px;
            height: 33.5px;
            background: url('@/assets/icons/my/tips-bg-new.png') no-repeat;
            background-size: 100% 100%;
            font-size: 13px;
            color: #CCCCCC;
            margin: 0 auto;
            line-height: 40px;
            text-align: center;
          }
          .bankLabel{
            color: #7D7C7C;
            font-size: 15px;
            font-weight: 400;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 8px;
            &.order{
              font-size: 13px;
              color: #999;
              margin-top: 7px;
            }
            img{
              width: 3.5px;
              height: 6.5px;fill: #7D7C7C;
              margin-left: 5.5px;
            }
          }

        }
        .label{
          font-size: 22px;
          font-weight: 500;
          color: #222222;
          line-height: 30px;
          display: flex;
          justify-content: center;
          align-items: center;
          img{
            width: 16px;
            height: 16px;
            margin-left: 3px;
          }
        }
        .tips{
          width: 107px;
          height: 35px;
          background: url('@/assets/icons/my/tips-bg.png') no-repeat;
          background-size: 100% 100%;
          font-size: 13px;
          color: #A8A8A8;
          margin: 0 auto;
          line-height: 40px;
          text-align: center;
        }
      }
      .repayment-date{
        margin: 6px 27px;
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #A8A8A8;
        line-height: 17px;
      }
      .period-content{
        background: #ffffff;
        // padding-bottom: 16px;
        margin: 10px;
        border-radius: 10px;

        .period-title{
          font-size: 16px;
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          color: #363636;
          line-height: 22px;
          padding: 15px 20px;
        }
        .period-list{
          // padding-left: 16px;
          border-radius: 8px;
          margin: 0 18px;
          padding-top: 3px;
        }
        .period-item{
          height: 72px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          // padding-right: 18px;
          color: #CCCCCC;
          position: relative;
          &.nosettle{
            .period-number{
              .period-date{
                color: #363636;
              }
              .period-current{
                color: #A8A8A8;
                .status{
                  color: #F43727;
                  border: 1px solid #F43727;
                }
                .number{
                  color: #000;
                }
              }
            }
            .period-amount{
              height: 50px;
              .repay-amount{
                color: #363636;
              }
              .period-status{
                color: #A8A8A8;

              }
            }
          }
          .period-number{
            .period-date{
              font-size: 15px;
              color: #999;
              font-weight: 400;
              line-height: 22px;
            }
            .period-current{
              margin-top: 2px;
              line-height: 17px;
              color: #999;
              font-size: 15px;
              font-weight: 400;
              .status{
                color: #999;
                font-size: 12px;
                font-weight: 400;
                height: 18px;
                line-height: 18px;
                border-radius: 2px;
                border: 1px solid #999;
                padding: 0 4px;
                margin-left: 4px;
              }
              .number{
                color: #999;
                font-size: 18px;
                font-weight: 400;
              }
            }
          }
          .period-amount{
              height: 50px;
            .repay-amount{
              font-size: 15px;
              font-weight: 500;
              line-height: 20px;
            }
            .period-status{
              font-size: 12px;
              font-weight: 500;
              line-height: 17px;
                .settle-img{
                  position: absolute;
                  right: 27px;
                  bottom: 0;
                  width: 65px;
                }
            }
          }
        }
        // .period-item +.period-item{
        //   border-top: 1px solid #d4d4d4;
        // }
      }
    }
    .repayment-fixed {
      position: fixed;
      bottom: 0;
      width: 100%;
    }
    .repayment-btn{
      margin: 20px 15px;
      height: 45px;
      border-radius: 22px;
      line-height: 45px;
      text-align: center;
      font-size: 16px;
      font-weight: 500;
      color: #FFFFFF;
      &.disabled{
        opacity: 0.5;
      }
    }
  }
</style>
