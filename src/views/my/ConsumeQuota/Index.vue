<template>
  <div class="consume-uota-page">
    <div class="consume-uota-page-header">
      <navigation-bar
        pageName="额度页 "
        :navBarStyle="{  color: '#fff', fontSize: '16px', fontWeight: 600 }"
        @onLeftClick="onBackClick"
      ></navigation-bar>
      <img class="banner" src="@/assets/images/my/consume-quota-banner.png" alt="">

      <MyMoneyCard :order-data="orderData" ref="myMoneyCard" />
    </div>
    <!-- 广告位 -->
     <AdConsumeQuota />

     <!-- 热销榜单 -->
      <GoodsList ref="goodsListRef" :listType="currentGoodsType" :isAdShow="true" />

  </div>
</template>

<script setup>
import MyMoneyCard from '@/components/MyMoneyCard/index.vue'
import AdConsumeQuota from './components/AdConsumeQuota.vue'
import GoodsList from '@/components/GoodsList/GoodsList.vue'
const { proxy } = getCurrentInstance()
const isIphoneX = window.isIphoneX
const router = useRouter()
const route = useRoute()
const store = useStore()
const loading = ref(false)
const finished = ref(false)
const refreshing = ref(false)
const lastGetListRequestId = ref(0)
const state = ref('')

const currentGoodsType = ref({
  type: 'region',
  value: proxy.$global.GOODS_REGION_FOLLOW,
  name: '热销榜单',
})



const onBackClick = () => {
  router.push({ name: 'Main', params: { componentName: '/my' } })
}

const goRepayment = (item) => {
  router.push({ path: '/my/bill/repayment', query: { billId: item.billId } })
}
// 下拉刷新
const onRefresh = () => {
  finished.value = false
  // 将 loading 设置为 true，表示处于加载状态
  loading.value = true
  resetQuery()
}





onMounted(() => {
})
</script>

<style lang="scss" scoped>
.consume-uota-page {
  height: 100%;
  width: 100%;
  background: #f4f6fa;
  overflow: auto;
  &-header {
    // background: url('@/assets/images/my/my-bg.png') no-repeat;
    background: linear-gradient(
      180deg,
      #5581f6 20%,
      #aabefb 50%,
      #d6dffa 65%,
      #e8edfa 75%,
      #f4f6fa 100%
    );
    background-size: 100% 100%;
    box-sizing: border-box;
    position: relative;
    .banner{
      display: block;
      width: 291px;
      margin: 25px auto 0 auto;
    }
  }
  .nav-bar {
    :deep(.left) {
      padding-left: 9px;
      color: #fff;
    }
    :deep(.page-title) {
      font-size: 16px;
      color: #fff;
    }
  }
}
.nav-bar {
  border-bottom: none;
}
</style>
