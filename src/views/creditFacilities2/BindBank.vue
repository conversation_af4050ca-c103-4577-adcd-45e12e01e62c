<template>
  <van-form
    ref="formRef"
    class="bind-bank"
    :show-error-message="true"
    validate-first
    @submit="submit"
  >
    <van-cell-group class="">
      <van-cell class="title-cell">
        <template #title>
          <p class="title">
            <span>还差最后一步</span>
            &nbsp;
            <span>绑定银行卡<strong>激活额度</strong></span>
          </p>
        </template>
        <template #label>
          <img src="./card.png" alt="" class="card-img" />
        </template>
      </van-cell>
    </van-cell-group>
    <div class="group2-wrap">
      <van-cell-group class="group2" :border="false">
        <van-field
          :model-value="form.custIdCard.name.replace(/(?!^)./g, '*')"
          label="持卡人"
          input-align="right"
          placeholder="请输入持卡人"
          :rules="[{ required: true, message: '请填写持卡人' }]"
          :border="false"
          readonly
        />
        <van-field
          :model-value="form.custIdCard.idcard"
          label="身份证号"
          input-align="right"
          placeholder="请输入身份证号"
          :rules="[{ required: true, message: '请填写身份证号' }]"
          :border="false"
          readonly
        />
        <van-field
          v-model.trim="form.bankCard.cardNo"
          label="银行卡号"
          input-align="right"
          placeholder="请输入银行卡号"
          :rules="[{ required: true, message: '请填写银行卡号' }]"
          :border="false"
          :formatter="cardNoFormatter"
          maxlength="30"
          @blur="getBankName()"
        />
        <van-field
          v-model.trim="form.bankName"
          label="所属银行"
          readonly
          input-align="right"
          @click="getBankName()"
          :border="false"
        />
        <van-field
          v-model.trim="form.bankCard.phone"
          label="手机号码"
          input-align="right"
          placeholder="请输入手机号码"
          :rules="[
            { required: true, message: '请填写手机号码' },
            {
              pattern: /1\d{10}/,
              message: '请填写正确的电话号码',
            },
          ]"
          :border="false"
          maxlength="11"
        />
        <van-field
          v-if="codeInputIsShow"
          v-model.trim="form.code"
          label="验证码"
          input-align="right"
          placeholder="请输入验证码"
          :rules="[{ required: true, message: '请填写验证码' }]"
          :border="false"
          maxlength="6"
        >
          <template #button>
            <van-button type="primary" size="small" :disabled="remaining > 0" @click="sendCode()">
              <span v-if="remaining">{{ remaining }}s</span>
              <span v-else>获取验证码</span>
            </van-button>
          </template>
        </van-field>
      </van-cell-group>
    </div>
    <div class="" style="margin: auto"></div>
    <IFooter class="footer" v-model:checked="checked">
      <template #agreement>
        已阅读并同意<strong
          @click.stop="
            agreementPopup.reveal({
              list: [
                { title: '账户委托扣款授权书', src: '/agreement/account_auth.htm' },
                { title: '数字证书协议', src: '/agreement/digital-certificate-protocol.htm' },
              ],
              active: 0,
            })
          "
          >《账户委托扣款授权书》</strong
        ><strong
          @click.stop="
            agreementPopup.reveal({
              list: [
                { title: '账户委托扣款授权书', src: '/agreement/account_auth.htm' },
                { title: '数字证书协议', src: '/agreement/digital-certificate-protocol.htm' },
              ],
              active: 1,
            })
          "
          >《数字证书协议》</strong
        >
      </template>
    </IFooter>
    <!-- <IframePopup ref="iframePopup"></IframePopup> -->
    <AgreementPopup ref="agreementPopup"></AgreementPopup>
  </van-form>
</template>

<script setup>
import { computed, onMounted, useTemplateRef } from 'vue'
import { useStore } from 'vuex'
import IFooter from './IFooter.vue'
// import IframePopup from '@/components/IframePopup.vue'
// const iframePopupRef = useTemplateRef('iframePopup')
import AgreementPopup from './AgreementPopup.vue'
const agreementPopup = useTemplateRef('agreementPopup')

import {
  saveBankCard,
  saveBindCard,
  getBankName as getBankNameApi,
  getBankSmsCode,
  getBankListGroup,
} from '@/api/bankcard'
import { useAsyncState, useCountdown } from '@vueuse/core'

import appJS from '@/utils/appJS.js'
import { showToast } from 'vant'

const store = useStore()
const user = computed(() => store.getters.userInfo)

const props = defineProps({
  bankChannelId: {
    type: String,
    required: true,
  },
  cmId: {
    type: String,
    required: true,
  },
  orderId: {
    type: String,
    // required: true,
  },
  productId: {
    type: String,
    // required: true,
  },
})
const emit = defineEmits(['submitted'])
const form = ref({
  bankCard: {
    channelId: null,
    phone: '',
    cardNo: '',
  },
  custIdCard: {
    name: '',
    idcard: '',
  },
  bankName: '',
  cmId: null,
  orderId: null,
  productId: null,
})
const checked = ref(true)
const codeInputIsShow = ref(false)

onMounted(() => {
  form.value.bankCard.channelId = props.bankChannelId
  form.value.cmId = props.cmId
  form.value.orderId = props.orderId
  form.value.productId = props.productId
  form.value.bankCard.phone = user.value.phone
  form.value.custIdCard.name = user.value.custIdCard.name
  form.value.custIdCard.idcard = user.value.custIdCard.idcard
})

const { execute: getBankName } = useAsyncState(
  async () => {
    if (form.value.bankCard.cardNo.length <= 15) return
    const res = await getBankNameApi({
      bankCard: {
        cardNo: form.value.bankCard.cardNo.replace(/\s/g, ''),
      },
    })
    return res.data.bankName
  },
  null,
  {
    immediate: false,
    onSuccess(bankName) {
      form.value.bankName = bankName
    },
  }
)

// function handleCardNoInputBlur() {
//   if (form.value.bankCard.cardNo.length <= 15) return
//   getBankName()
// }

const { remaining, start, stop, pause, resume } = useCountdown(0, {})

const { execute: sendCode } = useAsyncState(
  async () => {
    start(30)
    await getBankSmsCode({
      id: form.value.id,
    })
  },
  null,
  {
    immediate: false,
  }
)
const { execute: submit } = useAsyncState(
  async () => {
    // 首次点击
    if (!form.value.id) {
      const { data } = await saveBankCard(form.value)
      if (data.status === 'VALID') {
        emit('submitted', data)
        return
      }

      if (data.signMode === 'API') {
        form.value.id = data.id
        codeInputIsShow.value = true
        // sendCode()
        start(30)
        showToast('验证码已发送')
        return
      }

      if (data.signMode === 'PAGE') {
        appJS.appOtherWebView(res.data.submitUrl)
      }
      return
    }

    const { data } = await saveBindCard({
      id: form.value.id,
      verifyCode: form.value.code,
    })
    emit('submitted', data)
  },
  null,
  {
    immediate: false,
  }
)

function cardNoFormatter(val) {
  return val.replace(/\s/g, '')
}
</script>

<style lang="scss" scoped>
.bind-bank {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  position: relative;
  z-index: 0;
}
.footer {
  position: sticky;
  bottom: 0;
  margin-top: 10px;
  background-color: #fff;
  z-index: 10;
}
.title-cell {
  .card-img {
    width: calc(432px / 2);
    height: calc(288px / 2);
    display: block;
    margin: 24px auto 0;
    padding-left: 40px;
  }
  .title {
    color: #333;
    font-size: 14px;
    font-weight: 600;
    strong {
      color: #f00;
    }
  }
}
.group2-wrap {
  // margin: -40px 10px 0;
  margin-top: -70px;
  margin-bottom: 20px;
  padding: 24px 12px 12px;
  background: rgba(255, 255, 255, 0.8);
  background: url('./bankBg.png');
  background-size: 100% auto;
  background-repeat: no-repeat;
  z-index: 1;
  position: relative;
  // backdrop-filter: blur(10px);
  // -webkit-backdrop-filter: blur(10px);
}
.group2 {
  padding: 12px;
  background: #f9f9f9;
  border-radius: 8px;
  overflow: hidden;
  display: grid;
  gap: 8px;
  .van-cell {
    border-radius: 4px;
    overflow: hidden;
    border: 1px solid #e4e4e4;
  }
}
</style>
