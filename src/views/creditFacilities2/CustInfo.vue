<template>
  <van-form
    ref="formRef"
    class="cust-info"
    :show-error-message="true"
    validate-first
    @submit="submit"
  >
    <van-cell-group class="">
      <van-cell title="填写本人信息" label="准确填写有效信息，有助于额度审批"></van-cell>
    </van-cell-group>
    <van-cell-group class="" title="基本信息">
      <van-field
        v-model="form.educationName"
        label="学历信息"
        is-link
        readonly
        placeholder="请选择您的学历"
        input-align="right"
        :rules="[{ required: true, message: '请选择您的学历' }]"
        @click="
          pickerPopup
            .reveal({
              title: '学历信息',
              columns: EDUCATION_OPTIONS,
            })
            .then(
              ({ isCanceled, data }) =>
                !isCanceled &&
                assign(form, {
                  education: data.selectedOptions[0].value,
                  educationName: data.selectedOptions[0].text,
                })
            )
        "
      >
      </van-field>
      <van-field
        v-model="form.marriageName"
        label="婚姻状况"
        is-link
        readonly
        input-align="right"
        placeholder="请选择婚姻状况"
        :rules="[{ required: true, message: '请选择婚姻状况' }]"
        @click="
          pickerPopup
            .reveal({
              title: '婚姻状况',
              columns: MARRIAGE_OPTIONS,
            })
            .then(
              ({ isCanceled, data }) =>
                !isCanceled &&
                assign(form, {
                  marriage: data.selectedOptions[0].value,
                  marriageName: data.selectedOptions[0].text,
                })
            )
        "
      ></van-field>
      <van-field
        v-model="form.companyPositionName"
        label="职业"
        is-link
        readonly
        input-align="right"
        placeholder="请选择您的职业"
        :rules="[{ required: true, message: '请选择您的职业' }]"
        @click="
          pickerPopup
            .reveal({
              title: '职业',
              columns: COMPANY_POSITION_OPTIONS,
            })
            .then(
              ({ isCanceled, data }) =>
                !isCanceled &&
                assign(form, {
                  companyPosition: data.selectedOptions[0].value,
                  companyPositionName: data.selectedOptions[0].text,
                })
            )
        "
      ></van-field>
      <!-- <van-field
        v-model="form.educationName"
        label="月收入"
        is-link
        readonly
        input-align="right"
      ></van-field> -->
    </van-cell-group>
    <van-cell-group class="" title="工作信息">
      <van-field
        v-model.trim="form.company"
        label="公司名称"
        input-align="right"
        placeholder="如：***市*****有限公司"
        :rules="[{ required: true, message: '请填写公司名字' }]"
      ></van-field>
      <van-field
        v-model.trim="form.companyTele"
        label="办公电话"
        input-align="right"
        maxlength="13"
        placeholder="010-请填电话或手机号"
        :rules="[{ required: true, message: '请填电话或手机号' }]"
      ></van-field>
      <van-field
        :model-value="
          form.companyDistrict &&
          `${form.companyProvince ?? ''}/${form.companyCity ?? ''}/${form.companyDistrict ?? ''}`
        "
        label="工作地址"
        input-align="right"
        is-link
        readonly
        placeholder="点击选择省市区"
        :rules="[
          {
            validator(value) {
              if (!form.companyDistrict) {
                return '请选择省市区'
              }
              return true
            },
          },
        ]"
        @click="
          areaPopup
            .reveal({
              title: '工作地址',
              areaList,
            })
            .then(
              ({ isCanceled, data }) =>
                !isCanceled &&
                assign(form, {
                  companyAreaData: data.selectedOptions
                    .filter((item) => !!item)
                    .map((item) => item.text)
                    .join('/'),
                  companyProvince: data.selectedOptions[0].text,
                  companyCity: data.selectedOptions[1].text,
                  companyDistrict: data.selectedOptions[2].text,
                  companyDistrictCode: data.selectedOptions[2].code,
                })
            )
        "
      ></van-field>
      <van-field
        v-model.trim="form.companyAddr"
        label=""
        input-align="right"
        type="textarea"
        placeholder="请补充详细地址或住宅地址（邮寄送达地址） 如：XX路/街道XX号XX小区XX栋XX楼层及房间号"
        :rules="[{ required: true, message: '请填写详细地址' }]"
      ></van-field>
    </van-cell-group>
    <van-cell-group class="" title="家庭联系人">
      <van-field
        v-model.trim="form.contacts[0].name"
        label="家庭联系人"
        input-align="right"
        placeholder="请填写联系人姓名"
        :rules="[{ required: true, message: '请填写联系人姓名' }]"
      ></van-field>
      <van-field
        v-model.trim="form.contacts[0].phone"
        label="联系人电话"
        input-align="right"
        placeholder="请填写联系人电话"
        maxlength="11"
        :rules="[
          { required: true, message: '请填写联系人电话' },
          {
            pattern: /1\d{10}/,
            message: '请填写正确的电话号码',
          },
        ]"
      ></van-field>
      <van-field
        v-model="form.contacts[0].relationshipName"
        label="关系"
        is-link
        input-align="right"
        readonly
        placeholder="请选择联系人关系"
        :rules="[{ required: true, message: '请选择联系人关系' }]"
        @click="
          pickerPopup
            .reveal({
              title: '关系',
              columns: RELATIONSHOP1_OPTIONS,
            })
            .then(
              ({ isCanceled, data }) =>
                !isCanceled &&
                assign(form.contacts[0], {
                  relationship: data.selectedOptions[0].value,
                  relationshipName: data.selectedOptions[0].text,
                })
            )
        "
      ></van-field>
    </van-cell-group>
    <van-cell-group class="" title="紧急联系人">
      <van-field
        v-model.trim="form.contacts[1].name"
        label="其他联系人"
        input-align="right"
        placeholder="请填写联系人姓名"
        :rules="[{ required: true, message: '请填写联系人姓名' }]"
      ></van-field>
      <van-field
        v-model.trim="form.contacts[1].phone"
        label="联系人电话"
        input-align="right"
        placeholder="请填写联系人电话"
        maxlength="11"
        :rules="[
          { required: true, message: '请填写联系人电话' },
          {
            pattern: /1\d{10}/,
            message: '请填写正确的电话号码',
          },
        ]"
      ></van-field>
      <van-field
        v-model="form.contacts[1].relationshipName"
        label="关系"
        input-align="right"
        is-link
        readonly
        placeholder="请选择联系人关系"
        :rules="[{ required: true, message: '请选择联系人关系' }]"
        @click="
          pickerPopup
            .reveal({
              title: '关系',
              columns: RELATIONSHOP2_OPTIONS,
            })
            .then(
              ({ isCanceled, data }) =>
                !isCanceled &&
                assign(form.contacts[1], {
                  relationship: data.selectedOptions[0].value,
                  relationshipName: data.selectedOptions[0].text,
                })
            )
        "
      ></van-field>
    </van-cell-group>
    <div class="" style="margin: auto"></div>
    <IFooter class="footer" v-model:checked="checked">
      <template #agreement>
        已阅读并同意<strong
          @click.stop="
            agreementPopup.reveal({
              title: '授权相关协议',
              src: '/agreement/product-service.htm',
            })
          "
          >《授权相关协议》</strong
        >
      </template>
    </IFooter>
    <!-- <IframePopup ref="iframePopup"></IframePopup> -->
    <AgreementPopup ref="agreementPopup"></AgreementPopup>
    <van-popup v-model:show="pickerPopupIsShow" position="bottom" :safe-area-inset-bottom="true">
      <van-picker
        v-bind="pickerPopupProps"
        @confirm="pickerPopup.confirm"
        @cancel="pickerPopup.cancel"
      />
    </van-popup>
    <van-popup v-model:show="areaPopupIsShow" position="bottom">
      <van-area v-bind="areaPopupProps" @confirm="areaPopup.confirm" @cancel="areaPopup.cancel" />
    </van-popup>
  </van-form>
</template>

<script setup>
import { useAsyncState, useConfirmDialog, useToggle } from '@vueuse/core'
import global from '@/constant/Global.js'
import { isString, assign } from 'lodash-es'
import { areaList } from '@vant/area-data'
import { saveCustInfo } from '@/api/customer'
import { onMounted, useTemplateRef } from 'vue'
import IFooter from './IFooter.vue'
// import IframePopup from '@/components/IframePopup.vue'
import AgreementPopup from './AgreementPopup.vue'

// const iframePopupRef = useTemplateRef('iframePopup')
const agreementPopup = useTemplateRef('agreementPopup')
const emit = defineEmits(['submitted'])
const {
  EDUCATION_OPTIONS,
  RELATIONSHOP1_OPTIONS,
  RELATIONSHOP2_OPTIONS,
  MARRIAGE_OPTIONS,
  COMPANY_POSITION_OPTIONS,
} = global
const form = ref({
  education: '',
  educationName: '',
  liveStatus: '',
  liveStatusName: '',
  liveProvince: '',
  liveCity: '',
  liveDistrict: '',
  liveAddr: '',
  liveAreaData: '',
  liveDistrictCode: '',
  marriage: '',
  marriageName: '',
  spouseIdCard: '',
  spouseName: '',
  spousePhone: '',
  spouseCompany: '',
  companyPosition: '',
  companyPositionName: '',
  company: '',
  companyTele: '',
  companyAreaData: '',
  companyDistrict: '',
  companyDistrictCode: '',
  companyAddr: '',
  contacts: [
    {
      index: 1,
      name: '',
      phone: '',
      relationship: '',
    },
    {
      index: 2,
      name: '',
      phone: '',
      relationship: '',
    },
  ],
})
const checked = ref(true)
const [pickerPopupIsShow, togglePickerPopupIsShow] = useToggle(false)
const pickerPopupProps = ref({})
const pickerPopup = useConfirmDialog()
pickerPopup.onReveal((props) => {
  pickerPopupProps.value = props
  togglePickerPopupIsShow(true)
})
pickerPopup.onCancel(() => {
  togglePickerPopupIsShow(false)
})
pickerPopup.onConfirm(() => {
  togglePickerPopupIsShow(false)
})

const [areaPopupIsShow, toggleAreaPopupIsShow] = useToggle(false)
const areaPopupProps = ref({})
const areaPopup = useConfirmDialog()
areaPopup.onReveal((props) => {
  areaPopupProps.value = props
  toggleAreaPopupIsShow(true)
})
areaPopup.onCancel(() => {
  toggleAreaPopupIsShow(false)
})
areaPopup.onConfirm(() => {
  toggleAreaPopupIsShow(false)
})
async function submit() {
  await saveCustInfo({
    custInfo: form.value,
  })
  await store.dispatch('GetInfo')
  emit('submitted', form.value)
}

const store = useStore()
const user = computed(() => store.getters.userInfo)

onMounted(async () => {
  await store.dispatch('GetInfo')
  // form.value = user.value.custInfo
})
</script>

<style lang="scss" scoped>
.cust-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  :deep(.van-cell-group__title) {
    background: #fff;
    color: #333;
    font-size: 16px;
    font-weight: 600;
    margin-top: 10px;
    &::before {
      // color: #000;
      content: '';
      display: inline-block;
      width: 3px;
      height: 13px;
      background: #4671eb;
      border-radius: 999vw;
      margin-right: 4px;
    }
  }
  :deep(.van-cell__right-icon) {
    color: #cfcfcf;
  }
}
.footer {
  position: sticky;
  bottom: 0;
  margin-top: 10px;
  background-color: #fff;
  z-index: 10;
}
.group {
  // margin: 10px 0;
}
</style>
