<template>
  <footer class="footer">
    <p class="tip">
      <img src="@/assets/images/credit/safe.png" class="icon" />
      轻享花智能加密，承诺实时保障你的信息安全
    </p>
    <van-button block type="primary" native-type="submit" class="submit-btn"> 下一步 </van-button>
    <van-field
      :model-value="checked"
      :rules="[{ required: true, message: '请阅读并同意相关协议' }]"
      class="agreement-field"
    >
      <template #input>
        <van-checkbox v-model="checked">
          <slot name="agreement" :checked="checked"></slot>
        </van-checkbox>
      </template>
    </van-field>
  </footer>
</template>

<script setup>
const checked = defineModel('checked', { type: Boolean, default: true })
</script>

<style lang="scss" scoped>
.footer {
  // margin-top: auto;
  margin-top: 10px;
  position: sticky;
  bottom: 0;
  background-color: #fff;
  z-index: 10;
  padding: 18px;
  .tip {
    text-align: center;
    color: #bcbcbc;
    font-size: 13px;
    .icon {
      display: inline-block;
      width: 12px;
      height: 16px;
    }
  }
  .submit-btn {
    border-radius: 8px;
    background: linear-gradient(180deg, #3f58d6 0%, #a7bbfd 100%);
    border: none;
    margin-top: 8px;
  }
  .agreement-field {
    padding: 0;
    margin-top: 8px;
    max-width: 100%;
    :deep(.van-field__value) {
      flex: 1;
      flex-basis: 0;
      min-width: 0;
    }
    .van-checkbox {
      width: 100%;
      --van-checkbox-size: 18px;
      // --van-checkbox-checked-icon-color: #4671eb;
      :deep(.van-checkbox__label) {
        flex: 1;
        flex-basis: 0;
        font-size: 13px;
        color: #333;
        overflow-y: hidden;
        overflow-x: scroll;
        white-space: nowrap;
        strong {
          color: #2659eb;
          font-weight: inherit;
        }
      }
    }
  }
}
</style>
