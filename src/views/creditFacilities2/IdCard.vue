<template>
  <van-form
    ref="formRef"
    class="id-card"
    :show-error-message="true"
    validate-first
    @submit="submit"
  >
    <van-cell-group class="group1">
      <van-cell class="title-cell">
        <template #title>
          <div class="title-wrap">
            <span class="title-text"
              >请确保本人认证 <span>预估最高可得<strong>5000额度</strong></span></span
            >
            <span class="index"><strong>1</strong><span>/</span><span>3</span></span>
          </div>
        </template>
        <template #label> 应监督要求，证件与人脸信息将通过合作机构核验您的身份 </template>
      </van-cell>
      <!-- <van-cell> -->
      <van-field
        :modelValue="idCardFrontImgFile && idCardBackImgFile"
        name="idcardImg"
        :rules="[
          {
            validator(value, rule) {
              if (!idCardFrontImgFile) {
                return '请上传身份证头像面'
              }
              if (!idCardBackImgFile) {
                return '请上传身份证国徽面'
              }
              return true
            },
          },
        ]"
      >
        <template #input>
          <div class="" style="width: 100%">
            <div class="updater-wrap">
              <div class="updater front" @click="frontFileDialog.open()">
                <img
                  v-if="form.idcardFrontImg"
                  :src="form.idcardFrontImg"
                  alt=""
                  class="card-img"
                />
                <template v-else>
                  <img src="@/assets/images/credit/camera.png" alt="" class="camera" />
                  <div class="camera-tip">拍摄/身份证<strong>头像面</strong></div>
                </template>
              </div>
              <div class="updater back" @click="backFileDialog.open()">
                <img v-if="form.idcardBackImg" :src="form.idcardBackImg" alt="" class="card-img" />
                <template v-else>
                  <!-- <img src="@/assets/images/credit/idcard-front.png" alt="" /> -->
                  <img src="@/assets/images/credit/camera.png" alt="" class="camera" />
                  <div class="camera-tip">拍摄/身份证<strong>国徽面</strong></div>
                </template>
              </div>
            </div>
            <p class="pic-tips">确保证件边框完整，文字清晰可见</p>
          </div>
        </template>
        <!-- <template #label> 确保证件边框完整，文字清晰可见 </template> -->
        <!-- </van-cell> -->
      </van-field>
    </van-cell-group>
    <van-cell-group class="group2">
      <van-cell title="请确认您的信息无误" value="若有误请直接点击修改" class="title-cell" />
      <van-field
        v-model.trim="form.name"
        name="name"
        label="真实姓名"
        placeholder="请输入姓名"
        :rules="[{ required: true, message: '请输入姓名' }]"
      />
      <van-field
        v-model.trim="form.idcard"
        name="idcard"
        label="身份证号"
        placeholder="请输入身份证号"
        :rules="[{ required: true, message: '请输入身份证号' }]"
      />
    </van-cell-group>
    <div class="" style="margin: auto"></div>
    <IFooter class="footer" v-model:checked="checked">
      <template #agreement>
        已阅读并同意<strong
          @click.stop="
            agreementPopup
              .reveal({
                title: '授权相关协议',
                src: '/agreement/product-service.htm',
              })
              .then(({ isCanceled }) => !isCanceled && (checked = true))
          "
          >《授权相关协议》</strong
        >
      </template>
    </IFooter>
    <!-- <IframePopup ref="iframePopup"></IframePopup> -->
    <AgreementPopup ref="agreementPopup"></AgreementPopup>
  </van-form>
</template>

<script setup>
import { useAsyncState, useFileDialog, whenever } from '@vueuse/core'
import { ocr, saveCustIdCard } from '@/api/customer'
import { computed, onMounted, onWatcherCleanup, useTemplateRef, watch } from 'vue'
import dayjs from 'dayjs'
import { uploadBase64 } from '@/api/base'
import IFooter from './IFooter.vue'
// import IframePopup from '@/components/IframePopup.vue'
import AgreementPopup from './AgreementPopup.vue'

// const iframePopupRef = useTemplateRef('iframePopup')
const agreementPopup = useTemplateRef('agreementPopup')

const store = useStore()
const user = computed(() => store.getters.userInfo)

const emit = defineEmits(['submitted'])

const form = ref({
  name: null,
  idcard: null,
  gender: null,
  nation: null,
  birthday: null,
  idcardAddr: null,
  office: null,
  startTime: null,
  endTime: null,
  idcardFrontImg: null,
  idcardBackImg: null,
})

const checked = ref(true)

// 身份证图片文件
const idCardFrontImgFile = ref()
const idCardBackImgFile = ref()
// 身份证图片DataUrl
// const idCardFrontImgUrl = computed((oldValue) => {
//   oldValue && URL.revokeObjectURL(oldValue)
//   const file = idCardFrontImgFile.value
//   if (!file) return
//   const url = URL.createObjectURL(file)
//   return url
// })
// const idCardBackImgUrl = computed((oldValue) => {
//   oldValue && URL.revokeObjectURL(oldValue)
//   const file = idCardBackImgFile.value
//   if (!file) return
//   const url = URL.createObjectURL(file)
//   return url
// })

whenever(idCardFrontImgFile, (file) => {
  const url = URL.createObjectURL(file)
  onWatcherCleanup(() => {
    URL.revokeObjectURL(url)
  })
  form.value.idcardFrontImg = url
})
whenever(idCardBackImgFile, (file) => {
  const url = URL.createObjectURL(file)
  onWatcherCleanup(() => {
    URL.revokeObjectURL(url)
  })
  form.value.idcardBackImg = url
})

// 身份证图片压缩base64
const idCardFrontImgBase64 = ref()
const idCardBackImgBase64 = ref()

const frontFileDialog = useFileDialog({
  accept: 'image/jpeg,image/png',
  multiple: false,
  capture: 'environment',
  reset: true,
})
const backFileDialog = useFileDialog({
  accept: 'image/jpeg,image/png',
  multiple: false,
  capture: 'environment',
  reset: true,
})

// 压缩图片
async function compressImage(file, maxWidth, maxHeight, quality) {
  // const reader = new FileReader()
  const url = URL.createObjectURL(file)
  const img = new Image()
  img.src = url
  await new Promise((res, rej) => {
    img.onload = res
    img.onerror = rej
  })
  const canvas = document.createElement('canvas')
  const ctx = canvas.getContext('2d')
  let width = img.width
  let height = img.height
  if (width > height) {
    if (width > maxWidth) {
      height = Math.round((height * maxWidth) / width)
      width = maxWidth
    }
  } else {
    if (height > maxHeight) {
      width = Math.round((width * maxHeight) / height)
      height = maxHeight
    }
  }
  canvas.width = width
  canvas.height = height
  ctx.drawImage(img, 0, 0, width, height)
  const dataURL = canvas.toDataURL('image/jpeg', quality)
  return dataURL
  // return new Promise((rej) => {
  //   canvas.toBlob(rej, 'image/jpeg', quality)
  // })
}
// async function update(file, name) {
//   console.log(file, name)
//   return file
// }

frontFileDialog.onChange(async (files) => {
  const file = files?.[0]
  if (!file) return
  idCardFrontImgFile.value = file
  idCardFrontImgBase64.value = await compressImage(file, 2560, 1440, 0.8)
})
backFileDialog.onChange(async (files) => {
  const file = files?.[0]
  if (!file) return
  idCardBackImgFile.value = file
  idCardBackImgBase64.value = await compressImage(file, 2560, 1440, 0.8)
})

whenever(idCardFrontImgBase64, async (base64) => {
  // if (!file) return
  // const base64 = await compressImage(file, 2560, 1440, 0.8)
  // const base64 =
  try {
    const res = await ocr({
      infoImg: base64,
    })

    form.value.name = res.data.name
    form.value.idcard = res.data.idNo
    form.value.gender = {
      男: 1,
      女: 2,
    }[res.data.gender]
    form.value.nation = res.data.nation
    form.value.birthday = dayjs(res.data.birthDay).format('YYYY-MM-DD')
    form.value.idcardAddr = res.data.address
  } catch (error) {
    idCardFrontImgFile.value = null
    return
  }
})
whenever(idCardBackImgBase64, async (base64) => {
  // if (!file) return
  // const base64 = await compressImage(file, 2560, 1440, 0.8)
  try {
    const res = await ocr({
      emblemImg: base64,
    })
    form.value.office = res.data.issuedBy
    const [startTime, endTime] = res.data.validityPeriod.split('-')
    form.value.startTime = startTime
    form.value.endTime = endTime
  } catch (error) {
    idCardBackImgFile.value = null
  }
})

// const { state: idCardFrontImgCompressFile, execute: frontCompressExecute } = useAsyncState(
//   async () => {
//     if (!idCardFrontImgFile.value) return
//     return compressImage(idCardFrontImgFile.value, 2560, 1440, 0.8)
//   },
//   {}
// )
// watch(idCardFrontImgFile, (newValue) => {
//   frontCompressExecute()
// })

// const { state: idCardBackImgCompressFile } = useAsyncState(async () => {
//   if (!idCardBackImgFile.value) return
//   return compressImage(idCardBackImgFile.value, 2560, 1440, 0.8)
// })
// watch(idCardBackImgFile, (newValue) => {
//   backCompressExecute()
// })

// whenever([], async () => {})
// const { execute: ocrExecute } = useAsyncState(
//   async () => {
//     const [infoImg, emblemImg] = await Promise.all([
//       idCardFrontImgFile.value ? {} : null,
//       idCardBackImgFile.value ? {} : null,
//     ])

//     const res = await ocr({
//       infoImg,
//       emblemImg,
//     })
//   },
//   null,
//   {
//     immediate: false,
//   }
// )

// whenever([idCardFrontImgFile, idCardBackImgFile], (newValue) => {
//   // ocrExecute()
//   console.log(newValue)
// })
// function sumbit() {}
const formRef = useTemplateRef('formRef')
const { execute: submit } = useAsyncState(
  async () => {
    // formRef.value.submit()
    console.log('submit')

    const [idcardFrontImg, idcardBackImg] = await Promise.all([
      uploadBase64({ base64: idCardFrontImgBase64.value, fileName: 'idcardfront' }),
      uploadBase64({ base64: idCardBackImgBase64.value, fileName: 'idcardback' }),
    ])

    form.value.idcardFrontImg = idcardFrontImg.link
    form.value.idcardBackImg = idcardBackImg.link

    await saveCustIdCard({
      custIdCard: form.value,
    })

    await store.dispatch('GetInfo')

    emit('submitted', form.value)
  },
  null,
  {
    immediate: false,
  }
)
onMounted(async () => {
  await store.dispatch('GetInfo')

  // form.value.name = user.value.custIdCard.name
  // form.value = user.value.custIdCard
})
</script>

<style lang="scss" scoped>
.id-card {
  flex: 1;
  // background: red;
  // min-height: max-content ;
  display: flex;
  flex-direction: column;
  .footer {
    // margin-top: auto;
    // margin-top: 10px;
    // position: sticky;
    // bottom: 0;
    // background-color: #fff;
    // z-index: 10;
    // padding: 18px;
    // .tip {
    //   text-align: center;
    //   .icon {
    //     display: inline-block;
    //     width: 12px;
    //     height: 16px;
    //   }
    // }
    // .submit-btn {
    //   border-radius: 8px;
    //   background: linear-gradient(180deg, #3f58d6 0%, #a7bbfd 100%);
    //   border: none;
    // }
  }
}
.group1 {
  // background: #fff;
}
.title-cell {
  .title-wrap {
    display: flex;
  }
  .title-text {
    font-size: 14px;
    color: #000;
    font-weight: 600;
    strong {
      color: #f00;
    }
  }
  .index {
    margin-left: auto;
    font-size: 13px;
    color: #cfcfcf;
    strong {
      font-size: 15px;
      color: #333;
    }
  }
  :deep(.van-cell__label) {
    color: #cfcfcf;
  }
}
.group2 {
  margin-top: 10px;
}
.updater-wrap {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  height: 103px;
  gap: 6px;
  .updater {
    background: #f7f8fa;
    border-radius: 4px;
    padding: 4px;
    height: 103px;
    box-sizing: border-box;
    // height: 100%;
    // overflow: hidden;
    position: relative;
    background-size: calc(100% - 8px);
    background-repeat: no-repeat;
    background-position: 4px 4px;
    // background-clip: content-box;
    &.front {
      background-image: url('@/assets/images/credit/idcard-front.png');
    }
    &.back {
      background-image: url('@/assets/images/credit/idcard-back.png');
    }
    .card-img {
      width: 100%;
      height: 100%;
      display: block;
      border-radius: 4px;
    }
    .camera {
      width: 32px;
      height: 32px;
      display: block;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
    .camera-tip {
      position: absolute;
      left: 4px;
      right: 4px;
      // top: 20px;
      bottom: 14px;
      text-align: center;
      color: #333;
      font-size: 14px;
      // font-weight: 400;
      strong {
        color: #3f58d6;
        font-weight: inherit;
      }
    }
  }
}
.pic-tips {
  text-align: center;
  color: #0000004d;
  font-size: 14px;
  // font-weight: 400;
}
.group2 {
  .title-cell {
    :deep(.van-cell__title) {
      color: #333;
      font-size: 16px;
      font-weight: 600;
    }
    :deep(.van-cell__value) {
      color: #d5d5d5;
      font-size: 15px;
    }
  }
}
</style>
